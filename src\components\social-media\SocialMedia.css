/* Social Media Component Styles */

.social-media-container {
  display: flex;
  gap: 15px;
  align-items: center;
}

/* Layout variations */
.social-media-container.horizontal {
  flex-direction: row;
  flex-wrap: wrap;
}

.social-media-container.vertical {
  flex-direction: column;
}

.social-media-container.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

/* Size variations */
.social-media-container.small {
  gap: 10px;
}

.social-media-container.medium {
  gap: 15px;
}

.social-media-container.large {
  gap: 20px;
}

/* Social media link styles with Tamil Cultural Colors */
.social-media-link {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-decoration: none;
  color: #333333; /* Tamil cultural body text */
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  transition: all 0.3s ease;
  padding: 5px;
}

/* Icons-only layout adjustments */
.social-media-link:not(:has(.social-label)) {
  padding: 5px;
}

.social-media-link:hover {
  opacity: 0.8;
}

.social-media-link:active {
  transform: translateY(0);
}

/* Icon wrapper */
.social-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Social icons */
.social-icon {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
  display: block;
}

/* Size-specific icon dimensions */
.social-media-container.small .social-icon {
  width: 20px;
  height: 20px;
}

.social-media-container.medium .social-icon {
  width: 28px;
  height: 28px;
}

.social-media-container.large .social-icon {
  width: 36px;
  height: 36px;
}

/* Size-specific container dimensions for icons-only */
.social-media-container.small .social-media-link:not(:has(.social-label)) {
  padding: 3px;
}

.social-media-container.medium .social-media-link:not(:has(.social-label)) {
  padding: 5px;
}

.social-media-container.large .social-media-link:not(:has(.social-label)) {
  padding: 7px;
}

/* Social labels with Tamil Cultural Typography */
.social-label {
  font-weight: 500;
  font-size: 0.9rem;
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  color: #333333; /* Tamil cultural body text */
  white-space: nowrap;
  letter-spacing: 0.3px;
}

/* Size-specific label styles */
.social-media-container.small .social-label {
  font-size: 0.8rem;
}

.social-media-container.medium .social-label {
  font-size: 0.9rem;
}

.social-media-container.large .social-label {
  font-size: 1rem;
}

/* Platform-specific hover effects */
.social-media-link:hover .social-icon {
  transform: scale(1.1);
}

/* Specific platform colors on hover - removed styling, keeping only opacity change */

/* Responsive design */
@media (max-width: 768px) {
  .social-media-container {
    gap: 12px;
  }

  .social-media-container.horizontal {
    justify-content: center;
  }

  .social-media-link {
    padding: 5px;
  }

  .social-media-link:not(:has(.social-label)) {
    padding: 5px;
  }

  .social-label {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .social-media-container {
    gap: 10px;
  }
  
  .social-media-link {
    padding: 3px;
    gap: 6px;
  }
  
  .social-media-container.small .social-icon {
    width: 20px;
    height: 20px;
  }
  
  .social-media-container.medium .social-icon {
    width: 28px;
    height: 28px;
  }
  
  .social-media-container.large .social-icon {
    width: 36px;
    height: 36px;
  }
  
  .social-label {
    font-size: 0.75rem;
  }
}

/* Animation for mobile app opening */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.social-media-link.opening-app {
  animation: pulse 0.3s ease-in-out;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .social-media-link {
    color: #ecf0f1;
  }
}
