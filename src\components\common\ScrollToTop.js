import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * ScrollToTop component that automatically scrolls to the top of the page
 * whenever the route changes. This ensures that each page starts from the top
 * instead of preserving the scroll position from the previous page.
 */
const ScrollToTop = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    // Disable browser's scroll restoration to prevent conflicts
    if ('scrollRestoration' in window.history) {
      window.history.scrollRestoration = 'manual';
    }

    // Enhanced scroll to top function with multiple approaches
    const scrollToTop = () => {
      // Method 1: Modern scrollTo with instant behavior
      try {
        window.scrollTo({
          top: 0,
          left: 0,
          behavior: 'instant'
        });
      } catch (error) {
        // Method 2: Fallback for older browsers
        window.scrollTo(0, 0);
      }

      // Method 3: Direct DOM manipulation for better compatibility
      if (document.documentElement) {
        document.documentElement.scrollTop = 0;
      }
      if (document.body) {
        document.body.scrollTop = 0;
      }

      // Method 4: Force scroll on all scrollable containers
      const scrollableElements = document.querySelectorAll('[data-scroll-container], .App, html, body');
      scrollableElements.forEach(element => {
        if (element && typeof element.scrollTo === 'function') {
          element.scrollTo(0, 0);
        } else if (element) {
          element.scrollTop = 0;
        }
      });
    };

    // Execute immediately
    scrollToTop();

    // Execute with requestAnimationFrame for better timing
    const rafId = requestAnimationFrame(() => {
      scrollToTop();
    });

    // Execute after a small delay to ensure DOM is fully ready
    const timeoutId = setTimeout(() => {
      scrollToTop();
    }, 50);

    // Execute after a longer delay as final fallback
    const fallbackTimeoutId = setTimeout(() => {
      scrollToTop();
    }, 100);

    // Cleanup on unmount
    return () => {
      clearTimeout(timeoutId);
      clearTimeout(fallbackTimeoutId);
      cancelAnimationFrame(rafId);
    };
  }, [pathname]);

  // This component doesn't render anything
  return null;
};

export default ScrollToTop;
