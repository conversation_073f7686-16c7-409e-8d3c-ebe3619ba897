/* Font Face Declarations */
@font-face {
  font-family: 'MinionPro-Bold';
  src: url('../../assets/fonts/MinionPro-Bold_0.otf') format('opentype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'BebasKai';
  src: url('../../assets/fonts/BebasKai.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* Countdown Timer Wrapper - Fixed at Bottom */
.countdown-wrapper {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  z-index: 1000 !important;
  /* Force hardware acceleration and prevent parent transform interference */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  will-change: transform;
  /* Ensure it's positioned relative to viewport, not parent */
  margin: 0 !important;
  padding: 0 !important;
  /* Prevent any parent container from affecting positioning */
  isolation: isolate;
  contain: layout style paint;
  /* Additional fixes to prevent scrolling with content */
  top: auto !important;
  position: fixed !important;
  /* Force it to be positioned relative to viewport */
}

/* Hide countdown wrapper on mobile when team popup is open */
/* Only hide on mobile screens (768px and below) */
@media (max-width: 768px) {
  .countdown-wrapper.hidden-on-mobile {
    display: none !important;
  }
}

/* Remain visible on tablet and desktop screens */
@media (min-width: 769px) {
  .countdown-wrapper.hidden-on-mobile {
    display: block !important;
  }
}

/* Title section above the main container */
.countdown-title-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.countdown-title-icon {
  width: 20px;
  height: 20px;
  filter: brightness(0) invert(1);
}

.countdown-title {
  color: #ffffff;
  font-family: 'BebasKai', sans-serif;
  font-size: 0.8rem;
  letter-spacing: 2px;
  margin: 0;
  text-transform: uppercase;
}

/* Main countdown container with golden border */
.countdown-container {
  width: 100%;
  padding: 10px 40px 10px 40px;
  background: #0a7a64;
  border-top: 6px solid #ffffff;
  border-radius: 0;
  backdrop-filter: none;
  box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.4);
  opacity: 1;
  visibility: visible;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  margin: 0;
}

/* Left side - Date information */
.countdown-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #ffffff;
  font-family: 'BebasNeue', sans-serif;
}

.countdown-dates {
  font-size: 1.2rem;
  margin-bottom: 5px;
  letter-spacing: 2px;
  font-family: 'BebasKai', sans-serif;
  color: #ffffff;
}

.countdown-tamil-dates {
  font-size: 0.9rem;
  color: #000000;
  font-family: 'BebasKai', sans-serif;
}

/* Center - Timer section */
.countdown-center {
  flex: 2;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Right side - Location information */
.countdown-right {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.countdown-location {
  display: flex;
  align-items: center;
  gap: 8px;
}

.location-icon {
  width: 20px;
  height: 20px;
  filter: brightness(0);
}

.location-text {
  font-size: 1.2rem;
  color: #ffffff;
  font-family: 'BebasKai', sans-serif;
  letter-spacing: 1px;
}

/* Desktop layout - show all three sections */
@media (min-width: 769px) {
  .countdown-container {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: space-between !important;
    gap: 0 !important;
  }

  .countdown-left,
  .countdown-right {
    display: flex !important;
  }

  .countdown-center {
    flex: 1 !important;
  }
}

.countdown-timer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  width: 100%;
}
.countdown-dates {
  font-size: 1rem;
  margin-bottom: 5px;
  letter-spacing: 2px;
  font-family: 'BebasKai', sans-serif;
  color: #ffffff;
}
.location-text {
  font-size: 1rem;
  color: #ffffff;
  font-family: 'BebasKai', sans-serif;
  letter-spacing: 1px;
}
/* Top section - Numbers only */
.countdown-numbers-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 35px;
  flex-wrap: wrap;
}

.countdown-item {
  display: flex;
  align-items: center;
  position: relative;
}

.countdown-number {
  font-size: 1.5rem;
  font-weight: 800;
  color: #ffffff;
  /* White color */
  text-shadow: none;
  background: none;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: unset;
  background-clip: unset;
  font-family: 'MinionPro-Bold', serif;
  line-height: 1;
  margin: 0;
}

.countdown-separator {
  font-size: 2rem;
  font-weight: 800;
  color: #ffffff;
  /* White color */
  text-shadow: none;
  animation: none;
  line-height: 1;
  margin: 0 5px;
}

/* Horizontal divider between numbers and labels */
.countdown-divider {
  width: 50%;
  height: 2px;
  background: #ffffff;
  margin: 2px 0;
  border-radius: 1px;
}

/* Bottom section - Labels only */
.countdown-labels-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 3px;
  flex-wrap: wrap;
}

.countdown-label-item {
  display: flex;
  justify-content: center;
  min-width: 60px;
}

.countdown-label {
  font-size: 0.7rem;
  font-weight: 600;
  color: #ffffff;
  /* White color */
  text-transform: uppercase;
  letter-spacing: 1px;
  font-family: 'Baloo2', sans-serif;
  text-align: center;
}

/* Tablet responsive */
@media (max-width: 1024px) and (min-width: 769px) {
  .countdown-wrapper {
    position: fixed !important;
    bottom: 0px !important;
    left: 0px !important;
    right: 0px !important;
    width: 100vw !important;
    height: auto !important;
    z-index: 1000 !important;
  }

  .countdown-container {
    padding: 12px 30px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
  }

  .countdown-left,
  .countdown-right {
    display: flex;
    flex: 0.8;
  }

  .countdown-center {
    flex: 1;
    width: auto;
    display: flex;
    justify-content: center;
  }

  .countdown-timer {
    gap: 12px;
  }

  .countdown-numbers-section {
    gap: 20px;
  }

  .countdown-number {
    font-size: 1.8rem;
    color: #ffffff;
    /* White color */
    margin: 0;
    font-family: 'MinionPro-Bold', serif;
  }

  .countdown-separator {
    font-size: 2rem;
    color: #ffffff;
    /* White color */
    margin: 0 5px;
  }

  .countdown-divider {
    margin: 0px 0;
    width: 70%;
  }

  .countdown-labels-section {
    gap: 13px;
  }

  .countdown-label-item {
    min-width: 50px;
  }

  .countdown-label {
    font-size: 0.7rem;
    color: #ffffff;
    /* White color */
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .countdown-wrapper {
    position: fixed !important;
    bottom: 0px !important;
    left: 0px !important;
    right: 0px !important;
    width: 100vw !important;
    height: auto !important;
    z-index: 1000 !important;
  }

  .countdown-container {
    padding: 15px 20px 12px 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .countdown-left,
  .countdown-right {
    display: none; /* Hide on mobile - dates and location moved to subtitle area */
  }

  .countdown-center {
    flex: 1;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .countdown-timer {
    gap: 8px;
  }

  .countdown-numbers-section {
    gap: 20px;
  }

  .countdown-number {
    font-size: 1.8rem;
    color: #ffffff;
    /* White color */
    font-weight: 800;
    line-height: 1;
    margin: 0;
    font-family: 'MinionPro-Bold', serif;
  }

  .countdown-separator {
    font-size: 1.5rem;
    color: #ffffff;
    /* White color */
    font-weight: 800;
    margin: 0 2px;
  }

  .countdown-divider {
    width: 50%;
    margin: 0px 0;
  }

  .countdown-labels-section {
    gap: 8px;
  }

  .countdown-label-item {
    min-width: 45px;
  }

  .countdown-label {
    font-size: 0.7rem;
    color: #ffffff;
    /* White color */
    font-weight: 600;
  }
}
@media (max-width: 480px) {
.countdown-divider {
  width: 65%;
  height: 2px;
  background: #ffffff;
  margin: 8px 0;
  border-radius: 1px;
}
}