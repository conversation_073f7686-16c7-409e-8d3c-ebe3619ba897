// Production Error Suppression Utility
// This utility helps suppress known non-critical errors in production

class ErrorSuppression {
  constructor() {
    this.suppressedErrors = new Set();
    this.init();
  }

  init() {
    // Only apply error suppression in production
    // Don't rely on process.env as it may not be available in AWS Amplify
    const isProduction = (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'production') || window.location.hostname !== 'localhost';

    if (isProduction) {
      this.setupGlobalErrorHandling();
      this.setupConsoleFiltering();
      this.setupUnhandledRejectionHandling();
    }
  }

  setupGlobalErrorHandling() {
    const originalErrorHandler = window.onerror;
    
    window.onerror = (message, source, lineno, colno, error) => {
      if (this.shouldSuppressError(message, source)) {
        return true; // Suppress the error
      }
      
      // Call original handler if it exists
      if (originalErrorHandler) {
        return originalErrorHandler(message, source, lineno, colno, error);
      }
      
      return false;
    };
  }

  setupConsoleFiltering() {
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;
    
    console.error = (...args) => {
      const message = args.join(' ');
      if (!this.shouldSuppressConsoleMessage(message)) {
        originalConsoleError.apply(console, args);
      }
    };
    
    console.warn = (...args) => {
      const message = args.join(' ');
      if (!this.shouldSuppressConsoleMessage(message)) {
        originalConsoleWarn.apply(console, args);
      }
    };
  }

  setupUnhandledRejectionHandling() {
    window.addEventListener('unhandledrejection', (event) => {
      const reason = event.reason?.message || event.reason?.toString() || '';
      
      if (this.shouldSuppressError(reason)) {
        event.preventDefault();
        return;
      }
    });
  }

  shouldSuppressError(message, source = '') {
    const suppressPatterns = [
      // Google Forms related errors
      /docs\.google\.com.*font.*ERR_FILE_NOT_FOUND/i,
      /filesystem:https:\/\/docs\.google\.com.*ERR_FILE_NOT_FOUND/i,
      /KFO.*\.woff2.*ERR_FILE_NOT_FOUND/i,

      // Google Forms script errors
      /main\.js.*Uncaught Error/i,
      /search_impl\.js.*Error/i,
      /init_embed\.js.*Search endpoint requested/i,

      // YouTube embed related violations
      /Added non-passive event listener to a scroll-blocking.*touchstart/i,
      /Added non-passive event listener to a scroll-blocking.*touchmove/i,
      /Added non-passive event listener to a scroll-blocking.*touchend/i,
      /Added non-passive event listener to a scroll-blocking.*wheel/i,
      /www-embed-player\.js.*setTimeout.*handler took.*ms/i,
      /base\.js.*setTimeout.*handler took.*ms/i,
      /Forced reflow while executing JavaScript took.*ms/i,
      /youtube\.com.*embed.*player/i,

      // Common non-critical errors
      /Non-Error promise rejection captured/i,
      /Script error/i,
      /ResizeObserver loop limit exceeded/i,
      /Loading chunk \d+ failed/i,
      /ChunkLoadError/i,

      // Network related errors that don't affect functionality
      /Failed to fetch/i,
      /Network request failed/i,
      /ERR_NETWORK/i,

      // Passive event listener warnings
      /Added non-passive event listener/i,
      /Consider marking event handler as 'passive'/i
    ];

    const messageStr = message?.toString() || '';
    const sourceStr = source?.toString() || '';
    const combinedStr = `${messageStr} ${sourceStr}`;

    return suppressPatterns.some(pattern => pattern.test(combinedStr));
  }

  shouldSuppressConsoleMessage(message) {
    const suppressPatterns = [
      // Audio loading errors (we handle these gracefully)
      /Audio loading error:/i,

      // Google Forms warnings
      /Google Form iframe failed to load:/i,

      // YouTube embed violation warnings
      /\[Violation\].*Added non-passive event listener to a scroll-blocking.*touchstart/i,
      /\[Violation\].*Added non-passive event listener to a scroll-blocking.*touchmove/i,
      /\[Violation\].*Added non-passive event listener to a scroll-blocking.*touchend/i,
      /\[Violation\].*Added non-passive event listener to a scroll-blocking.*wheel/i,
      /\[Violation\].*setTimeout.*handler took.*ms/i,
      /\[Violation\].*Forced reflow while executing JavaScript took.*ms/i,
      /\[Violation\].*Added non-passive event listener/i,

      // YouTube embed script warnings
      /www-embed-player\.js.*Violation/i,
      /base\.js.*Violation/i,
      /youtube\.com.*embed/i,

      // Font loading errors from Google Forms
      /GET.*filesystem:.*fonts.*ERR_FILE_NOT_FOUND/i,

      // Development warnings in production
      /Warning: ReactDOM\.render is no longer supported/i,
      /Warning: componentWillReceiveProps has been renamed/i
    ];

    return suppressPatterns.some(pattern => pattern.test(message));
  }

  // Method to manually suppress specific error types
  addSuppressedError(pattern) {
    this.suppressedErrors.add(pattern);
  }

  // Method to check if an error should be logged for debugging
  shouldLogForDebugging(error) {
    // In development, log everything
    const isDevelopment = (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'development') || window.location.hostname === 'localhost';

    if (isDevelopment) {
      return true;
    }

    // In production, only log critical errors
    return !this.shouldSuppressError(error?.message || error?.toString());
  }
}

// Create and export singleton instance
const errorSuppression = new ErrorSuppression();

export default errorSuppression;

// Export utility functions
export const logError = (error, context = '') => {
  if (errorSuppression.shouldLogForDebugging(error)) {
    console.error(`[${context}]`, error);
  }
};

export const logWarning = (warning, context = '') => {
  if (errorSuppression.shouldLogForDebugging({ message: warning })) {
    console.warn(`[${context}]`, warning);
  }
};
