import React from 'react';
import './Schools.css';
import { logWarning } from '../../utils/errorSuppression';
import OptimizedYouTubeEmbed from '../common/OptimizedYouTubeEmbed';
import { useResponsiveImage, IMAGE_CATEGORIES } from '../../hooks/useResponsiveImage';

const Schools = ({ language = 'english' }) => {
  // Debug logging for AWS Amplify deployment
  // Don't rely on process.env as it may not be available in AWS Amplify
  const isProduction = (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'production') || window.location.hostname !== 'localhost';

  // Responsive school activity images
  const reconnectImage = useResponsiveImage('reconnect.jpg', IMAGE_CATEGORIES.SCHOOLS, 'schools/reconnect.jpg');
  const animalsImage = useResponsiveImage('Interact with Animals.jpg', IMAGE_CATEGORIES.SCHOOLS, 'schools/Interact with Animals.jpg');
  const handicraftImage = useResponsiveImage('Traditional Handicraft.jpg', IMAGE_CATEGORIES.SCHOOLS, 'schools/Traditional Handicraft.jpg');
  const gamesImage = useResponsiveImage('Tradtional Games.jpg', IMAGE_CATEGORIES.SCHOOLS, 'schools/Tradtional Games.jpg');
  const foodImage = useResponsiveImage('Native Food.jpg', IMAGE_CATEGORIES.SCHOOLS, 'schools/Native Food.jpg');
  const detoxImage = useResponsiveImage('Digital Detox.jpg', IMAGE_CATEGORIES.SCHOOLS, 'schools/Digital Detox.jpg');

  // Responsive school poster images
  const school1Poster = useResponsiveImage('school1.jpeg', IMAGE_CATEGORIES.POSTERS, 'schools/school1.jpeg');
  const school2Poster = useResponsiveImage('school2.jpeg', IMAGE_CATEGORIES.POSTERS, 'schools/school2.jpeg');

  if (isProduction) {
    logWarning(`[Schools] School1 poster path: ${school1Poster}`, 'Schools');
    logWarning(`[Schools] School2 poster path: ${school2Poster}`, 'Schools');
    logWarning(`[Schools] Environment: ${(typeof process !== 'undefined' && process.env && process.env.NODE_ENV) || 'unknown'}`, 'Schools');
    logWarning(`[Schools] Public URL: ${(typeof process !== 'undefined' && process.env && process.env.PUBLIC_URL) || 'undefined'}`, 'Schools');
    logWarning(`[Schools] Window origin: ${window.location.origin}`, 'Schools');
    logWarning(`[Schools] Current URL: ${window.location.href}`, 'Schools');
  }

  const content = {
    english: {
      heading: "Inspiring the Next Generation",
      subtitle: "This year at Sempozhil, we are curating our content with children at the heart of everything we do. Watching them engage with and enjoy the event last year reaffirmed our belief that they are our true inspiration and future.",
      mission: "We want to open their eyes to the choices they have and inspire them to reconnect with their roots.",
      eventTitle: "Experience Sempozhil 2025",
      activities: [
        {
          title: "Reconnect with their Roots",
          content: "Discover the beauty and wisdom of rural living, Observe and participate in eco-friendly practices rooted in village life.",
          image: reconnectImage
        },
        {
          title: "Interact with Farm Animals",
          content: "Enjoy close-up experiences with animals, discovering their habits and role in village life.",
          image: animalsImage
        },
        {
          title: "Try Traditional Handicrafts",
          content: "Engage in hands-on activities that introduce them to indigenous art forms and time-honored skills.",
          image: handicraftImage
        },
        {
          title: "Play Traditional Village Games",
          content: "Experience fun, active games passed down through generations that build teamwork and cultural connection.",
          image: gamesImage
        },
        {
          title: "Explore Native Food Diversity",
          content: "Taste and learn about traditional dishes made from local, seasonal ingredients—celebrating the richness of regional cuisine.",
          image: foodImage
        },
        {
          title: "Digital Detox & Mindful Exploration",
          content: "Step away from screens and into real-world discovery, creativity, and imagination.",
          image: detoxImage
        }
      ],
      videoTitle: "Experience Sempozhil",
      formTitle: "Registration"
    },
    tamil: {
      heading: "அடுத்த தலைமுறையை ஊக்குவித்தல்",
      subtitle: "இந்த ஆண்டு செம்பொழிலில், நாங்கள் செய்யும் எல்லாவற்றிலும் குழந்தைகளை மையமாக வைத்து எங்கள் உள்ளடக்கத்தை தயாரித்து வருகிறோம். கடந்த ஆண்டு அவர்கள் நிகழ்வில் ஈடுபட்டு மகிழ்வதைப் பார்த்தது, அவர்கள் எங்கள் உண்மையான உத்வேகம் மற்றும் எதிர்காலம் என்ற எங்கள் நம்பிக்கையை மீண்டும் உறுதிப்படுत்தியது.",
      mission: "அவர்களுக்கு இருக்கும் தேர்வுகளை அவர்களின் கண்களுக்கு திறக்க விரும்புகிறோம்—மற்றும் அவர்களின் வேர்களுடன் மீண்டும் இணைக்க ஊக்குவிக்க விரும்புகிறோம்.",
      eventTitle: "செம்பொழில் 2025 ஐ அனுபவிக்கவும்",
      activities: [
        {
          title: "அவர்களின் வேர்களுடன் மீண்டும் இணைதல்",
          content: "கிராமப்புற வாழ்க்கையின் அழகு மற்றும் ஞானத்தை கண்டறியுங்கள், கிராம வாழ்க்கையில் வேரூன்றிய சுற்றுச்சூழல் நட்பு நடைமுறைகளை கவனித்து பங்கேற்கவும்.",
          image: reconnectImage
        },
        {
          title: "பண்ணை விலங்குகளுடன் தொடர்பு",
          content: "விலங்குகளுடன் நெருக்கமான அனுபவங்களை அனுபவிக்கவும், அவற்றின் பழக்கவழக்கங்கள் மற்றும் கிராம வாழ்க்கையில் அவற்றின் பங்கை கண்டறியவும்.",
          image: animalsImage
        },
        {
          title: "பாரம்பரிய கைவினைப்பொருட்களை முயற்சிக்கவும்",
          content: "பூர்வீக கலை வடிவங்கள் மற்றும் காலங்காலமாக கடைபிடிக்கப்படும் திறன்களை அறிமுகப்படுத்தும் நடைமுறை செயல்பாடுகளில் ஈடுபடுங்கள்.",
          image: handicraftImage
        },
        {
          title: "பாரம்பரிய கிராம விளையாட்டுகள்",
          content: "தலைமுறைகளாக கடத்தப்பட்ட வேடிக்கையான, செயலில் உள்ள விளையாட்டுகளை அனுபவிக்கவும், அவை குழுப்பணி மற்றும் கலாச்சார தொடர்பை உருவாக்குகின்றன.",
          image: gamesImage
        },
        {
          title: "உள்ளூர் உணவு பன்முகத்தன்மையை ஆராயுங்கள்",
          content: "உள்ளூர், பருவகால பொருட்களிலிருந்து தயாரிக்கப்பட்ட பாரம்பரிய உணவுகளை சுவைத்து கற்றுக்கொள்ளுங்கள்—பிராந்திய உணவு வகைகளின் செழுமையை கொண்டாடுங்கள்.",
          image: foodImage
        },
        {
          title: "டிஜிட்டல் டிடாக்ஸ் & கவனமுள்ள ஆய்வு",
          content: "திரைகளிலிருந்து விலகி உண்மையான உலக கண்டுபிடிப்பு, படைப்பாற்றல் மற்றும் கற்பனையில் நுழையுங்கள்.",
          image: detoxImage
        }
      ],
      videoTitle: "செம்பொழிலை அனுபவிக்கவும்",
      formTitle: "பதிவு"
    }
  };

  const currentContent = content[language];

  return (
    <div className="schools-container">
      {/* Header Section */}
      <div className="schools-header">
        <h1 className="schools-heading">{currentContent.heading}</h1>
        <p className="schools-subtitle">{currentContent.subtitle}</p>
        <p className="mission-text">{currentContent.mission}</p>
      </div>

      {/* Video Section */}
      <div className="video-section">
        <OptimizedYouTubeEmbed
          videoId="D5iOcgDqFEY"
          title="Sempozhil Schools Video"
          autoplay={false}
          muted={true}
          enableLazyLoad={true}
          showPlayButton={true}
          className="schools-video"
          height="400px"
        />
      </div>

      {/* Introduction Section */}
      <div className="introduction-section">
        <h3 className="event-title">{currentContent.eventTitle}</h3>
      </div>

      {/* Activities Cards Section */}
      <div className="activities-section">
        <div className="activities-grid">
          {currentContent.activities.map((activity, index) => (
            <div key={index} className="activity-card">
              <div className="activity-image">
                <img src={activity.image} alt={activity.title} className="activity-img" />
              </div>
              <h4 className="activity-title">{activity.title}</h4>
              <p className="activity-description">{activity.content}</p>
            </div>
          ))}
        </div>
      </div>

      {/* School Posters Section */}
      <div className="posters-section">
        <div className="posters-container">
          <div className="poster-item">
            <img
              src={school1Poster}
              alt="School Program Poster 1"
              className="poster-image"
              onError={(e) => {
                logWarning(`[Schools] Failed to load school1 poster: ${school1Poster}`, 'Schools');
                console.error('School poster 1 loading error:', e);
              }}
              onLoad={() => {
                logWarning(`[Schools] Successfully loaded school1 poster: ${school1Poster}`, 'Schools');
              }}
            />
          </div>
          <div className="poster-item">
            <img
              src={school2Poster}
              alt="School Program Poster 2"
              className="poster-image"
              onError={(e) => {
                logWarning(`[Schools] Failed to load school2 poster: ${school2Poster}`, 'Schools');
                console.error('School poster 2 loading error:', e);
              }}
              onLoad={() => {
                logWarning(`[Schools] Successfully loaded school2 poster: ${school2Poster}`, 'Schools');
              }}
            />
          </div>
        </div>
      </div>

      {/* Pricing Section */}
      <div className="pricing-section">
        <h3 className="pricing-title">Package</h3>
        <div className="pricing-options">
          <div className="pricing-option">
            <span className="price">Rs 300</span>
            <span className="package-details">Guided Tour + Welcome Drink + Traditional Snack Pack</span>
          </div>
          <div className="pricing-option">
            <span className="price">Rs 400</span>
            <span className="package-details">Guided Tour + Welcome Drink + Traditional Meal (Lunch)</span>
          </div>
        </div>
      </div>

      {/* Registration Form Section */}
      <div className="form-section">
        <h3 className="form-title">{currentContent.formTitle}</h3>
        <div className="form-container">
          <iframe
            src="https://docs.google.com/forms/d/e/1FAIpQLSd8eQ_ohY4hvLrYVE2pctPTcKkBk6Tct4OS_3-Lp18E5n_n_g/viewform?embedded=true&usp=pp_url"
            width="100%"
            height="1200"
            frameBorder="0"
            marginHeight="0"
            marginWidth="0"
            scrolling="auto"
            title="Registration Form"
            style={{ border: 'none', background: 'transparent', overflow: 'auto' }}
            className="form-iframe"
            sandbox="allow-scripts allow-forms allow-same-origin allow-popups allow-popups-to-escape-sandbox"
            loading="lazy"
            onError={(e) => {
              logWarning('Google Form iframe failed to load', 'Schools Form');
              e.target.style.display = 'none';
              const fallback = e.target.nextElementSibling;
              if (fallback && fallback.classList.contains('form-fallback')) {
                fallback.style.display = 'block';
              }
            }}
          >
            Loading…
          </iframe>

          {/* Fallback link if iframe doesn't work properly */}
          

          {/* Always visible button to open form in new tab */}
          <div className="form-direct-access" style={{ textAlign: 'center' }}>
            <p>You can also access the form directly:</p>
            <a
              href="https://docs.google.com/forms/d/e/1FAIpQLSd8eQ_ohY4hvLrYVE2pctPTcKkBk6Tct4OS_3-Lp18E5n_n_g/viewform"
              target="_blank"
              rel="noopener noreferrer"
              className="form-direct-link"
              style={{
                display: 'inline-block',
                padding: '12px 24px',
                backgroundColor: '#e67e22',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '5px',
                fontWeight: 'bold',
                marginTop: '10px'
              }}
            >
              Open Registration Form in New Tab
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Schools;
