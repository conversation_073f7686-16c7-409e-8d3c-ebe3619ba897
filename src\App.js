import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import Nav from "../src/components/navigation/Navbar"
import Sustainability from './components/sustainability/Sustainability';
import Homepage from './components/homepage/Homepage';
import StallBooking from './components/stallbooking/StallBooking';
import Schools from './components/schools/Schools';
import Sponsors from './components/sponsors/Sponsors';
import Team from './components/team/Team';
import Footer from './components/footer/Footer';
import GlobalAudioPlayer from './components/audio/GlobalAudioPlayer';
import CountdownTimer from './components/common/CountdownTimer';
import ErrorBoundary from './components/common/ErrorBoundary';
import PerformanceMonitor from './components/common/PerformanceMonitor';
import GoogleAnalytics, { initGA } from './components/analytics/GoogleAnalytics';
import ScrollToTop from './components/common/ScrollToTop';
import './App.css';

// Component to conditionally render countdown timer
const ConditionalCountdownTimer = ({ language, isTeamPopupOpen }) => {
  const location = useLocation();

  // Don't show countdown timer on homepage since it has its own
  const showCountdownTimer = !['/home', '/'].includes(location.pathname);

  return showCountdownTimer ? (
    <ErrorBoundary>
      <CountdownTimer language={language} isTeamPopupOpen={isTeamPopupOpen} />
    </ErrorBoundary>
  ) : null;
};

function App() {
  const [language, setLanguage] = useState('english');
  const [isTeamPopupOpen, setIsTeamPopupOpen] = useState(false);

  // Initialize Google Analytics on app start
  useEffect(() => {
    initGA();
  }, []);

  const handleLanguageChange = (newLanguage) => {
    setLanguage(newLanguage);
  };

  const handleTeamPopupToggle = (isOpen) => {
    setIsTeamPopupOpen(isOpen);
  };

  return (
    <ErrorBoundary>
      <Router>
        <ScrollToTop />
        <div className="App">
          <PerformanceMonitor />
          <GoogleAnalytics />
          <Nav onLanguageChange={handleLanguageChange} />
          <ErrorBoundary>
            <Routes>
              <Route path="/" element={<Homepage language={language} />} />
              <Route path="/home" element={<Homepage language={language} />} />
              <Route path="/schools" element={<Schools language={language} />} />
              <Route path="/stall-booking" element={<StallBooking language={language} />} />
              <Route path="/sustainability" element={<Sustainability language={language} />} />
              <Route path="/sponsors" element={<Sponsors language={language} />} />
              <Route path="/team" element={<Team language={language} onPopupToggle={handleTeamPopupToggle} />} />
              <Route path="*" element={<Homepage language={language} />} />
            </Routes>
          </ErrorBoundary>
          <Footer language={language} />
          <ConditionalCountdownTimer language={language} isTeamPopupOpen={isTeamPopupOpen} />
          <ErrorBoundary>
            <GlobalAudioPlayer />
          </ErrorBoundary>
        </div>
      </Router>
    </ErrorBoundary>
  );
}

export default App;
