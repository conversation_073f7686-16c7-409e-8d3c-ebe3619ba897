/* Team Member Card Styles */
.team-member-card {
  width: 100%;
  padding: 0.5rem 0;
  animation: cardFadeIn 1.2s linear;
}

@keyframes cardFadeIn {
  0% {
    opacity: 0;
    transform: translateY(200px);
  }
  80% {
    opacity: 1;
  }
  100% {
    transform: translateY(0px);
  }
}

.card {
  display: flex;
  flex-direction: column;
  padding: 0.8rem 0.5rem; /* Reduced left/right padding from 1rem to 0.5rem */
  border-radius: 0.6rem;
  color: #333333; /* Tamil cultural body text color */
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.7); /* More transparent background */
  height: 500px; /* Increased height to accommodate larger description section */
  justify-content: flex-start; /* Changed from space-between to flex-start for better control */
  align-items: center;
  text-align: center;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(75, 0, 0, 0.15); /* Deep Maroon border */
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden; /* Prevent content overflow */
}

.card:hover {
  background-color: rgba(255, 255, 255, 0.85);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(75, 0, 0, 0.15); /* Deep Maroon shadow */
  border: 1px solid rgba(75, 0, 0, 0.25);
}

.img-container {
  width: 140px; /* Reduced from 200px */
  height: 140px; /* Reduced from 200px */
  background-color: #ffffff;
  padding: 0.4rem;
  border-radius: 50%;
  margin: 0 auto 0.8rem auto; /* Further reduced bottom margin */
  transition: transform 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card:hover .img-container {
  transform: scale(0.95);
}

.img-container img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.member-name {
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  font-weight: 700;
  font-size: 1.4rem; /* Reduced from 2rem */
  margin: 0 0 0.5rem 0; /* Further reduced bottom margin */
  color: #4B0000; /* Deep Maroon */
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.3px;
  line-height: 1.1; /* Tighter line height */
}

.member-info {
  margin-bottom: 0.8rem; /* Consistent spacing */
  display: flex;
  flex-direction: column;
  justify-content: center; /* Center the content vertically */
  min-height: 80px; /* Fixed height for consistency */
  max-height: 80px; /* Prevent expansion */
}

.member-info p {
  margin: 0.15rem 0; /* Further reduced from 0.3rem */
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px; /* Reduced from 1px */
  color: #333333; /* Dark Gray */
  line-height: 1.2; /* Tighter line height */
  font-size: 0.85rem; /* Added smaller font size */
}

.member-role {
  font-size: 0.9rem; /* Reduced from 1.1rem */
  font-weight: 600;
  color: #4B0000; /* Deep Maroon */
  word-wrap: break-word; /* Handle long text */
  hyphens: auto; /* Allow hyphenation */
}

.member-company {
  font-size: 0.8rem; /* Reduced from 1rem */
  color: #00796B; /* Peacock Teal */
  font-weight: 500;
  word-wrap: break-word; /* Handle long text */
  hyphens: auto; /* Allow hyphenation */
}

.member-location {
  font-size: 0.75rem; /* Reduced from 0.9rem */
  color: #666666;
  font-weight: 400;
  word-wrap: break-word; /* Handle long text */
}

.member-description {
  margin: 0.8rem 0;
  padding: 0.6rem 0.3rem; /* Added horizontal padding */
  border-top: 1px solid rgba(75, 0, 0, 0.1);
  border-bottom: 1px solid rgba(75, 0, 0, 0.1);
  min-height: 80px; /* Fixed height to maintain consistent spacing */
  max-height: 80px; /* Prevent expansion */
  height: 80px; /* Enforce exact height even when empty */
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: visible; /* Allow text to be fully visible */
}

.description-line {
  margin: 0.12rem 0; /* Consistent margin */
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 1rem; /* Increased font size for better readability */
  color: #4B0000; /* Deep Maroon */
  font-weight: 600; /* Consistent weight */
  text-align: center;
  line-height: 1.3; /* Better line height for readability */
  word-wrap: break-word;
}

.know-more-section {
  margin: 0.8rem 0; /* Consistent spacing */
  flex-shrink: 0; /* Prevent shrinking */
}

.know-more-btn {
  padding: 0.5rem 1rem; /* Consistent padding */
  font-size: 0.8rem; /* Consistent font size */
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: linear-gradient(135deg, #DAA520, #B8860B); /* Traditional Gold gradient */
  color: white;
  font-weight: 700;
  border: none;
  border-radius: 6px; /* Smaller radius */
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.3px; /* Further reduced letter spacing */
}

.know-more-btn:hover {
  background: linear-gradient(135deg, #B8860B, #9A7209);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(218, 165, 32, 0.4);
}

.social-media {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem; /* Consistent gap */
  margin-top: auto; /* Push to bottom */
  margin-bottom: 0.5rem; /* Bottom spacing */
  min-height: 40px; /* Fixed height for consistency */
  max-height: 40px; /* Prevent expansion */
  flex-wrap: nowrap; /* Prevent wrapping to avoid overflow */
  flex-shrink: 0; /* Prevent shrinking */
  overflow: hidden; /* Hide overflow */
}

.social-link {
  width: 35px; /* Reduced from 50px */
  height: 35px; /* Reduced from 50px */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
  border-radius: 50%; /* Make circular */
  background-color: rgba(255, 255, 255, 0.1); /* Light background */
  border: 1px solid rgba(255, 255, 255, 0.2); /* Subtle border */
      border-radius: 50%;

  backdrop-filter: blur(10px); /* Glass effect */
}

.social-link:hover {
  transform: scale(1.15); /* Reduced from 1.2 */
  border-radius: 50%;
}

.social-link img {
  width: 24px; /* Reduced from 35px */
  height: 24px; /* Reduced from 35px */
    border-radius: 50%;
  transition: all 0.3s ease;
  filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.3));
}

.social-link:hover img {
}

/* Mobile Responsive */
@media screen and (max-width: 768px) {
  .img-container {
    width: 110px; /* Further reduced for mobile */
    height: 110px;
    margin: 0 auto 0.6rem auto; /* Reduced margin */
  }

  .member-name {
    font-size: 1.1rem; /* Further reduced for mobile */
    margin: 0 0 0.4rem 0; /* Reduced margin */
  }

  .social-link img {
    width: 18px; /* Further reduced for mobile */
    height: 18px;
  }

  .card {
    padding: 0.6rem 0.4rem; /* Further reduced padding */
    height: 440px; /* Increased height for mobile to accommodate larger description */
  }

  .member-info {
    margin-bottom: 0.6rem;
    min-height: 70px; /* Fixed height for mobile consistency */
    max-height: 70px;
  }

  .member-info p {
    margin: 0.1rem 0; /* Minimal margins for mobile */
    font-size: 0.75rem; /* Smaller text for mobile */
  }

  .member-description {
    min-height: 70px; /* Fixed height for mobile consistency */
    max-height: 70px;
    height: 70px; /* Enforce exact height even when empty */
    margin: 0.6rem 0;
    padding: 0.4rem 0.2rem; /* Adjusted padding for mobile */
  }

  .description-line {
    font-size: 0.85rem; /* Increased for mobile readability */
    line-height: 1.2; /* Better line height for mobile */
    margin: 0.1rem 0; /* Consistent margin */
  }

  .social-media {
    min-height: 35px; /* Fixed height for mobile */
    max-height: 35px;
  }
}

/* Tablet Responsive */
@media screen and (min-width: 769px) and (max-width: 991px) {
  .card {
    height: 480px; /* Increased height for tablet to accommodate larger description */
    padding: 0.7rem 0.4rem;
  }

  .img-container {
    width: 120px;
    height: 120px;
    margin: 0 auto 0.6rem auto;
  }

  .member-info {
    min-height: 75px; /* Fixed height for tablet consistency */
    max-height: 75px;
  }

  .member-description {
    min-height: 75px; /* Fixed height for tablet consistency */
    max-height: 75px;
    height: 75px; /* Enforce exact height even when empty */
    padding: 0.5rem 0.25rem; /* Adjusted padding for tablet */
  }

  .description-line {
    font-size: 0.9rem; /* Tablet-specific font size */
    line-height: 1.25; /* Better line height for tablet */
  }

  .social-media {
    min-height: 38px; /* Fixed height for tablet */
    max-height: 38px;
  }
}

/* Desktop Responsive */
@media screen and (min-width: 992px) {
  .img-container {
    width: 130px; /* Further reduced from 150px */
    height: 130px;
    margin: 0 auto 0.7rem auto; /* Reduced margin */
  }

  .card {
    padding: 0.8rem 0.4rem; /* Reduced padding with minimal left/right */
    height: 500px; /* Increased height for desktop to accommodate larger description */
  }

  .member-info {
    min-height: 80px; /* Fixed height for desktop consistency */
    max-height: 80px;
  }

  .member-description {
    min-height: 80px; /* Fixed height for desktop consistency */
    max-height: 80px;
    height: 80px; /* Enforce exact height even when empty */
    padding: 0.6rem 0.3rem; /* Proper padding for desktop */
  }

  .description-line {
    font-size: 1rem; /* Desktop font size for optimal readability */
    line-height: 1.3; /* Optimal line height for desktop */
  }

  .social-media {
    min-height: 40px; /* Fixed height for desktop */
    max-height: 40px;
  }
}

/* Large Desktop - 24 inch monitors */
@media screen and (min-width: 1400px) {
  .card {
    height: 520px; /* Increased height for large screens to accommodate larger description */
    padding: 0.9rem 0.5rem; /* Slightly more padding for large screens */
  }

  .img-container {
    width: 140px; /* Reduced from 160px */
    height: 140px;
  }

  .member-info {
    min-height: 80px; /* Fixed height for large screen consistency */
    max-height: 80px;
  }

  .member-description {
    min-height: 80px; /* Fixed height for large screen consistency */
    max-height: 80px;
    height: 80px; /* Enforce exact height even when empty */
    padding: 0.6rem 0.3rem; /* Proper padding for large screens */
  }

  .description-line {
    font-size: 1.05rem; /* Slightly larger for large screens */
    line-height: 1.3; /* Optimal line height for large screens */
  }

  .social-media {
    min-height: 40px; /* Fixed height for large screens */
    max-height: 40px;
  }
}
