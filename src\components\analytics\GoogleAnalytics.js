import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { isAnalyticsEnabled, getMeasurementId } from '../../config/analytics';

// Initialize Google Analytics
export const initGA = () => {
  const GA_MEASUREMENT_ID = getMeasurementId();

  // Check if analytics should be enabled
  if (!isAnalyticsEnabled()) {
    console.log('Google Analytics not initialized - disabled for this environment');
    return;
  }

  // Check if gtag is already loaded (from HTML head)
  if (typeof window.gtag !== 'undefined') {
    console.log('Google Analytics already initialized from HTML head with ID:', GA_MEASUREMENT_ID);
    return;
  }

  // Load Google Analytics script (fallback if not loaded from HTML)
  const script = document.createElement('script');
  script.async = true;
  script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
  document.head.appendChild(script);

  // Initialize gtag
  window.dataLayer = window.dataLayer || [];
  function gtag() {
    window.dataLayer.push(arguments);
  }
  window.gtag = gtag;

  gtag('js', new Date());
  gtag('config', GA_MEASUREMENT_ID);

  console.log('Google Analytics initialized with ID:', GA_MEASUREMENT_ID);
};

// Track page views
export const trackPageView = (path, title) => {
  const GA_MEASUREMENT_ID = getMeasurementId();
  if (typeof window.gtag !== 'undefined' && isAnalyticsEnabled()) {
    window.gtag('config', GA_MEASUREMENT_ID, {
      page_path: path,
      page_title: title,
    });
  }
};

// Track custom events
export const trackEvent = (action, category, label, value) => {
  if (typeof window.gtag !== 'undefined' && isAnalyticsEnabled()) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};

// Track button clicks
export const trackButtonClick = (buttonName, location) => {
  trackEvent('click', 'button', `${buttonName} - ${location}`);
};

// Track form submissions
export const trackFormSubmission = (formName) => {
  trackEvent('submit', 'form', formName);
};

// Track file downloads
export const trackDownload = (fileName, fileType) => {
  trackEvent('download', 'file', `${fileName} - ${fileType}`);
};

// Track external link clicks
export const trackExternalLink = (url, linkText) => {
  trackEvent('click', 'external_link', `${linkText} - ${url}`);
};

// React component for automatic page tracking
const GoogleAnalytics = () => {
  const location = useLocation();

  useEffect(() => {
    // Track page view on route change
    const pageTitle = document.title || 'Sempozhil 2025';
    trackPageView(location.pathname + location.search, pageTitle);
  }, [location]);

  return null; // This component doesn't render anything
};

export default GoogleAnalytics;
