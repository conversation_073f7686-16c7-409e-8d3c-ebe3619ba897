import React, { useState, useEffect } from 'react';
import './CountdownTimer.css';
import instrumentIcon from '../../assets/instrument.png';
import locationIcon from '../../assets/locationicon.png';

const CountdownTimer = ({ language = 'english', isTeamPopupOpen = false }) => {
  const [timeLeft, setTimeLeft] = useState({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  const [secondsVisible, setSecondsVisible] = useState(true);

  const content = {
    english: {
      countdown: {
        title: "Event Starts In",
        days: "Days",
        hours: "Hours",
        minutes: "Minutes",
        seconds: "Seconds"
      }
    },
    tamil: {
      countdown: {
        title: "நிகழ்வு தொடங்குகிறது",
        days: "நாட்கள்",
        hours: "மணிநேரங்கள்",
        minutes: "நிமிடங்கள்",
        seconds: "விநாடிகள்"
      }
    }
  };

  const currentContent = content[language];

  // Countdown timer logic
  useEffect(() => {
    const targetDate = new Date('2025-08-21T09:00:00').getTime();

    const timer = setInterval(() => {
      const now = new Date().getTime();
      const difference = targetDate - now;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ days, hours, minutes, seconds });
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Blinking effect for seconds
  useEffect(() => {
    const blinkTimer = setInterval(() => {
      setSecondsVisible(prev => !prev);
    }, 500);

    return () => clearInterval(blinkTimer);
  }, []);

  // Generate dynamic class name based on popup state
  // When team popup is open, hide countdown wrapper on mobile screens only
  // Desktop/tablet screens will continue to show the countdown wrapper
  const countdownWrapperClass = `countdown-wrapper ${isTeamPopupOpen ? 'hidden-on-mobile' : ''}`;

  return (
    <div className={countdownWrapperClass}>
      {/* Title above the golden border */}
      <div className="countdown-title-wrapper">
        <img src={instrumentIcon} alt="Instrument" className="countdown-title-icon" />
        <h3 className="countdown-title">{currentContent.countdown.title}</h3>
      </div>

      {/* Main countdown container with golden border */}
      <div className="countdown-container">
        {/* Left side - Date information */}
        <div className="countdown-left">
          <div className="countdown-dates">Aug 21-24 &nbsp;&nbsp;Aavani 5-8</div>
          <div className="countdown-tamil-dates"></div>
        </div>

        {/* Center - Timer section */}
        <div className="countdown-center">
          <div className="countdown-timer">
            {/* Top section - Numbers only */}
            <div className="countdown-numbers-section">
              <div className="countdown-item">
                <div className="countdown-number">{timeLeft.days}:</div>
              </div>
              <div className="countdown-item">
                <div className="countdown-number">{timeLeft.hours}:</div>
              </div>
              <div className="countdown-item">
                <div className="countdown-number">{timeLeft.minutes}:</div>
              </div>
              <div className="countdown-item countdown-seconds">
                <div
                  className="countdown-number"
                  style={{
                    opacity: secondsVisible ? 1 : 0,
                    visibility: secondsVisible ? 'visible' : 'hidden',
                    transition: 'opacity 0.1s ease-in-out'
                  }}
                >
                  {timeLeft.seconds}
                </div>
              </div>
            </div>

            {/* Horizontal divider */}
            <div className="countdown-divider"></div>

            {/* Bottom section - Labels only */}
            <div className="countdown-labels-section">
              <div className="countdown-label-item">
                <div className="countdown-label">{currentContent.countdown.days}</div>
              </div>
              <div className="countdown-label-item">
                <div className="countdown-label">{currentContent.countdown.hours}</div>
              </div>
              <div className="countdown-label-item">
                <div className="countdown-label">{currentContent.countdown.minutes}</div>
              </div>
              <div className="countdown-label-item">
                <div className="countdown-label">{currentContent.countdown.seconds}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Location information */}
        <div className="countdown-right">
          <div className="countdown-location">
            <img src={locationIcon} alt="Location" className="location-icon" />
            <div className="location-text">YMCA Nandanam, Chennai</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CountdownTimer;
