import React, { useState, useRef, useCallback, useEffect } from 'react';
import { logWarning } from '../../utils/errorSuppression';
import './OptimizedYouTubeEmbed.css';

const OptimizedYouTubeEmbed = ({
  videoId,
  title = "YouTube Video",
  autoplay = false,
  muted = true,
  className = "",
  width = "100%",
  height = "400",
  enableLazyLoad = true,
  showPlayButton = true,
  onLoad = null,
  onError = null
}) => {
  const [isLoaded, setIsLoaded] = useState(!enableLazyLoad);
  const [isPlaying, setIsPlaying] = useState(false);
  const [hasError, setHasError] = useState(false);
  const iframeRef = useRef(null);
  const containerRef = useRef(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!enableLazyLoad) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !isLoaded) {
            setIsLoaded(true);
            observer.disconnect();
          }
        });
      },
      {
        rootMargin: '50px',
        threshold: 0.1
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [enableLazyLoad, isLoaded]);

  // Handle iframe load
  const handleIframeLoad = useCallback(() => {
    try {
      if (onLoad) onLoad();
    } catch (error) {
      logWarning('YouTube iframe load callback error', 'OptimizedYouTubeEmbed');
    }
  }, [onLoad]);

  // Handle iframe error
  const handleIframeError = useCallback((error) => {
    logWarning('YouTube iframe failed to load', 'OptimizedYouTubeEmbed');
    setHasError(true);
    if (onError) onError(error);
  }, [onError]);

  // Handle play button click
  const handlePlayClick = useCallback(() => {
    if (iframeRef.current && !hasError) {
      try {
        // Update iframe src to start playing
        const currentSrc = iframeRef.current.src;
        const newSrc = currentSrc.includes('autoplay=1') 
          ? currentSrc 
          : `${currentSrc}${currentSrc.includes('?') ? '&' : '?'}autoplay=1`;
        
        iframeRef.current.src = newSrc;
        setIsPlaying(true);
      } catch (error) {
        logWarning('YouTube play button error', 'OptimizedYouTubeEmbed');
        setHasError(true);
      }
    }
  }, [hasError]);

  // Build YouTube URL with optimized parameters
  const buildYouTubeUrl = useCallback(() => {
    const baseUrl = `https://www.youtube.com/embed/${videoId}`;
    const params = new URLSearchParams({
      // Performance optimizations
      rel: '0',           // Don't show related videos
      modestbranding: '1', // Minimal YouTube branding
      iv_load_policy: '3', // Don't show annotations
      fs: '1',            // Allow fullscreen
      enablejsapi: '1',   // Enable JS API for better control
      
      // Autoplay settings
      autoplay: autoplay ? '1' : '0',
      mute: muted ? '1' : '0',
      
      // Additional performance settings
      playsinline: '1',   // Better mobile performance
      widget_referrer: window.location.origin
    });

    return `${baseUrl}?${params.toString()}`;
  }, [videoId, autoplay, muted]);

  // Render loading placeholder
  const renderPlaceholder = () => (
    <div className="youtube-placeholder">
      <div className="youtube-placeholder-content">
        <div className="youtube-placeholder-icon">▶</div>
        <p>Click to load video</p>
        <button 
          className="youtube-load-button"
          onClick={() => setIsLoaded(true)}
          aria-label={`Load ${title}`}
        >
          Load Video
        </button>
      </div>
    </div>
  );

  // Render error state
  const renderError = () => (
    <div className="youtube-error">
      <div className="youtube-error-content">
        <p>Video could not be loaded</p>
        <a 
          href={`https://www.youtube.com/watch?v=${videoId}`}
          target="_blank"
          rel="noopener noreferrer"
          className="youtube-fallback-link"
        >
          Watch on YouTube
        </a>
      </div>
    </div>
  );

  return (
    <div 
      ref={containerRef}
      className={`optimized-youtube-embed ${className} ${isPlaying ? 'playing' : ''}`}
      style={{ width, height }}
    >
      {!isLoaded ? (
        renderPlaceholder()
      ) : hasError ? (
        renderError()
      ) : (
        <div className="youtube-iframe-container">
          <iframe
            ref={iframeRef}
            src={buildYouTubeUrl()}
            title={title}
            width="100%"
            height="100%"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowFullScreen
            loading="lazy"
            onLoad={handleIframeLoad}
            onError={handleIframeError}
            className="youtube-iframe"
          />
          
          {showPlayButton && !isPlaying && !autoplay && (
            <div className="youtube-play-overlay">
              <button 
                className="youtube-play-button"
                onClick={handlePlayClick}
                aria-label={`Play ${title}`}
              >
                <div className="youtube-play-icon">▶</div>
                <span>Play Video</span>
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default OptimizedYouTubeEmbed;
