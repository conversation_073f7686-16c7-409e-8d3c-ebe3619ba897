import React from 'react';
import './StallBooking.css';
import { logWarning } from '../../utils/errorSuppression';

// Note: Sponsor images are now handled in Footer.js component

const StallBooking = ({ language = 'english' }) => {

  const content = {
    english: {
      heading: "Stall Registration",
      mainContent: "Join us at India's largest Urban Village Festival—a vibrant celebration of tradition, community, and sustainability that attracts thousands of enthusiastic visitors.",
      subContent: "If you're a business owner looking to gain visibility and connect with a wide, conscious audience in a dynamic, culturally rich setting, this is your moment.",
      callToAction: "Limited stalls available - book yours now!",
      eventTheme: "Event Theme",
      themeDescription: "Urban Village Festival - Celebrating tradition, community, and sustainability",
      businessTypes: "Business Types Welcome",
      businessList: [
        "Food & Beverage Vendors",
        "Handicrafts & Artisans",
        "Sustainable Products",
        "Local Farmers & Producers",
        "Cultural Organizations",
        "Educational Institutions",
        "Technology Startups",
        "Health & Wellness"
      ],
      contactInfo: "Contact Information",
      contactText: "For inquiries and stall bookings, please contact us:",
      contactNumber: "+91 74186 48123",
      formTitle: "Registration Form",
      partnersTitle: "Our Trusted Partners",
      partnerDescription: "Join these leading organizations in supporting sustainable community development"
    },
    tamil: {
      heading: "கடை பதிவு",
      mainContent: "இந்தியாவின் மிகப்பெரிய நகர்ப்புற கிராம திருவிழாவில் எங்களுடன் சேருங்கள்—பாரம்பரியம், சமூகம் மற்றும் நிலைத்தன்மையின் துடிப்பான கொண்டாட்டம்.",
      subContent: "நீங்கள் ஒரு வணிக உரிமையாளராக இருந்து, ஒரு ஆற்றல்மிக்க, கலாச்சார நிறைந்த சூழலில் பரந்த, விழிப்புணர்வுள்ள பார்வையாளர்களுடன் தொடர்பு கொள்ள விரும்பினால், இதுவே உங்கள் தருணம்.",
      callToAction: "வரையறுக்கப்பட்ட கடைகள் கிடைக்கின்றன - உங்களுடையதை இப்போதே பதிவு செய்யுங்கள்!",
      eventTheme: "நிகழ்வு தீம்",
      themeDescription: "நகர்ப்புற கிராம திருவிழா - பாரம்பரியம், சமூகம் மற்றும் நிலைத்தன்மையைக் கொண்டாடுதல்",
      businessTypes: "வரவேற்கப்படும் வணிக வகைகள்",
      businessList: [
        "உணவு மற்றும் பானம் விற்பனையாளர்கள்",
        "கைவினைப்பொருட்கள் மற்றும் கலைஞர்கள்",
        "நிலையான தயாரிப்புகள்",
        "உள்ளூர் விவசாயிகள் மற்றும் உற்பத்தியாளர்கள்",
        "கலாச்சார அமைப்புகள்",
        "கல்வி நிறுவனங்கள்",
        "தொழில்நுட்ப ஸ்டார்ட்அப்கள்",
        "சுகாதாரம் மற்றும் நல்வாழ்வு"
      ],
      contactInfo: "தொடர்பு தகவல்",
      contactText: "விசாரணைகள் மற்றும் கடை பதிவுகளுக்கு, தயவுசெய்து எங்களை தொடர்பு கொள்ளுங்கள்:",
      contactNumber: "+91 74186 48123",
      formTitle: "பதிவு படிவம்",
      partnersTitle: "எங்கள் நம்பகமான கூட்டாளர்கள்",
      partnerDescription: "நிலையான சமூக வளர்ச்சியை ஆதரிப்பதில் இந்த முன்னணி அமைப்புகளுடன் சேருங்கள்"
    }
  };

  // Note: Sponsor data is now handled in Footer.js component

  const currentContent = content[language];

  return (
    <div className="stallbooking-container">
      {/* Page Heading */}
      <h1 className="stallbooking-heading">{currentContent.heading}</h1>

      {/* Main Content Section */}
      <div className="main-content-section">
        <div className="content-card">
          <p className="main-content">{currentContent.mainContent}</p>
          <p className="sub-content">{currentContent.subContent}</p>
          <div className="call-to-action">
            <p className="cta-text">{currentContent.callToAction}</p>
          </div>
        </div>
      </div>

      {/* Contact Info Section */}
      <div className="contact-info-section">
        <div className="content-card">
          <div className="contact-info-section">
            <p className="contact-text">{currentContent.contactText}</p>
            <a
              href={`tel:+91${currentContent.contactNumber.replace(/\s+/g, '').replace('+91', '')}`}
              className="contact-number-link"
              style={{
                color: '#e67e22',
                textDecoration: 'none',
                fontWeight: 'bold',
                fontSize: '1.2rem',
                display: 'inline-block',
                padding: '8px 16px',
                border: '2px solid #e67e22',
                borderRadius: '8px',
                transition: 'all 0.3s ease',
                backgroundColor: 'transparent'
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = '#e67e22';
                e.target.style.color = 'white';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = 'transparent';
                e.target.style.color = '#e67e22';
              }}
            >
              📞 {currentContent.contactNumber}
            </a>
          </div>
        </div>
      </div>

      {/* Event Theme Section */}
      {/* <div className="theme-section">
        <h2 className="section-title">{currentContent.eventTheme}</h2>
        <p className="theme-description">{currentContent.themeDescription}</p>
      </div> */}

      {/* Business Types Section */}
      {/* <div className="business-types-section">
        <h2 className="section-title">{currentContent.businessTypes}</h2>
        <div className="business-grid">
          {currentContent.businessList.map((business, index) => (
            <div key={index} className="business-item">
              <span className="business-icon">🏪</span>
              <span className="business-name">{business}</span>
            </div>
          ))}
        </div>
      </div> */}

      {/* Contact Section */}
      {/* <div className="contact-section">
        <h2 className="section-title">{currentContent.contactInfo}</h2>
        <div className="contact-details">
          <div className="contact-item">
            <span className="contact-icon">📞</span>
            <span className="contact-text">{currentContent.contactNumber}</span>
          </div>
        </div>
      </div> */}

      {/* Partners Section */}
      {/* <div className="partners-section">
        <h2 className="section-title">{currentContent.partnersTitle}</h2>
        <p className="partners-description">{currentContent.partnerDescription}</p>
        <div className="partners-showcase">
          {sponsors.map((sponsor, index) => (
            <div key={index} className="partner-card">
              <div className="partner-logo">
                <img src={sponsor.logo} alt={sponsor.name} />
              </div>
              <div className="partner-info">
                <h3 className="partner-name">{sponsor.name}</h3>
                <p className="partner-tier">{sponsor.tier}</p>
                <p className="partner-category">{sponsor.category}</p>
              </div>
            </div>
          ))}
        </div>
      </div> */}

      {/* Registration Form Section */}
      <div className="form-section">
        <h2 className="section-title">{currentContent.formTitle}</h2>
        <div className="form-container">
          <iframe
            src="https://docs.google.com/forms/d/e/1FAIpQLSeetoYisW9-Svs7Hu1dpGCqR04baGxlIiqaSXtI_pUuWVI9mA/viewform?embedded=true"
            width="640"
            height="13636"
            frameBorder="0"
            marginHeight="0"
            marginWidth="0"
            scrolling="auto"
            title="Stall Registration Form"
            className="google-form-iframe"
            sandbox="allow-scripts allow-forms allow-same-origin allow-popups allow-popups-to-escape-sandbox"
            loading="lazy"
            style={{
              border: 'none',
              background: 'transparent',
              overflow: 'auto'
            }}
            onError={(e) => {
              logWarning('Google Form iframe failed to load', 'Stall Booking Form');
              e.target.style.display = 'none';
              const fallback = e.target.nextElementSibling?.nextElementSibling;
              if (fallback && fallback.classList.contains('form-fallback-enhanced')) {
                fallback.style.display = 'block';
              }
            }}
          >
            Loading…
          </iframe>

          {/* Enhanced Fallback link for error handling */}
          <div className="form-fallback-enhanced" style={{ display: 'none', textAlign: 'center', marginTop: '20px', padding: '20px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
            <p>The form couldn't load properly. Please access it directly:</p>
            <a
              href="https://docs.google.com/forms/d/e/1FAIpQLSeetoYisW9-Svs7Hu1dpGCqR04baGxlIiqaSXtI_pUuWVI9mA/viewform"
              target="_blank"
              rel="noopener noreferrer"
              style={{
                display: 'inline-block',
                padding: '12px 24px',
                backgroundColor: '#4CAF50',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '6px',
                fontWeight: 'bold',
                fontSize: '16px'
              }}
            >
              Open Stall Registration Form
            </a>
          </div>

          {/* Fallback link if iframe doesn't work properly */}
          <div className="form-fallback" style={{ textAlign: 'center', marginTop: '110px' }}>
            <p>If the form doesn't load properly, you can access it directly:</p>
            <a
              href="https://docs.google.com/forms/d/e/1FAIpQLSeetoYisW9-Svs7Hu1dpGCqR04baGxlIiqaSXtI_pUuWVI9mA/viewform"
              target="_blank"
              rel="noopener noreferrer"
              className="form-direct-link"
              style={{
                display: 'inline-block',
                padding: '12px 24px',
                backgroundColor: '#e67e22',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '5px',
                fontWeight: 'bold',
                marginTop: '10px'
              }}
            >
              Open Registration Form in New Tab
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StallBooking;
