# Sempozhil Image Audit Report

## Current Image Inventory

### 1. Background Images (Hero/Landing)
**Current Usage:** Main background images for different devices
- `src/assets/homepage/landingl.jpg` - Laptop background (used in App.css)
- `src/assets/homepage/landingm.jpg` - 24-inch monitor background (used in App.css)
- `src/assets/landing pageMobile.jpg` - Mobile background (used in Team.css)

**Analysis:** These are the primary hero background images that need device-specific optimization.

### 2. Gallery/Activity Images (Homepage slideshow)
**Current Usage:** Homepage image slideshow gallery
- `src/assets/homepage/cycling.jpg` - Activity image
- `src/assets/homepage/jumping.jpg` - Activity image
- `src/assets/homepage/karthick.jpg` - Performance image
- `src/assets/homepage/karupannan.jpg` - Traditional art image
- `src/assets/homepage/kuththattam.jpg` - Cultural activity
- `src/assets/homepage/melam.jpg` - Traditional music
- `src/assets/homepage/poikaalkuthirai.jpg` - Dance performance
- `src/assets/homepage/sakthi.jpg` - Performance image
- `src/assets/homepage/ural.jpg` - Traditional tool
- `src/assets/homepage/uri.jpg` - Traditional game
- `src/assets/homepage/uriyadi.jpg` - Festival game

**Analysis:** These are medium-sized gallery images that need responsive sizing for different devices.

### 3. Snapshot/Statistics Images
**Current Usage:** Statistics display on homepage
- `src/assets/homepage/snapshot/30K foot fall 5K children.jpg`
- `src/assets/homepage/snapshot/200+ Animals 40+ Breeds.jpg`
- `src/assets/homepage/snapshot/100+ Performance Artists.jpg`
- `src/assets/homepage/snapshot/20+ exhibits.jpg`
- `src/assets/homepage/snapshot/150+ stalls.jpg`
- `src/assets/homepage/snapshot/50k footfall 15k children.jpg`
- `src/assets/homepage/snapshot/250+ animals 40+ breads.jpg`
- `src/assets/homepage/snapshot/150+ performance artists.jpg`
- `src/assets/homepage/snapshot/25+ exhibits the land, it's history, the people.jpg`
- `src/assets/homepage/snapshot/250+ stalls.jpg`
- `src/assets/homepage/snapshot/20+ Traditional Skills.jpg`

**Analysis:** These are informational graphics that need to be readable on all devices.

### 4. Media Coverage/Spotlight Images
**Current Usage:** Print media and TV coverage display
- `src/assets/homepage/spotlights/Picture1.png` - Print media coverage
- `src/assets/homepage/spotlights/Picture2.png` - Print media coverage
- `src/assets/homepage/spotlights/govt1.png` - Government coverage
- `src/assets/homepage/spotlights/govt2.png` - Government coverage
- `src/assets/homepage/spotlights/tv1.png` - TV channel logo
- `src/assets/homepage/spotlights/tv2.png` - TV channel logo
- `src/assets/homepage/spotlights/tv3.png` - TV channel logo
- `src/assets/homepage/spotlights/tv4.png` - TV channel logo
- `src/assets/homepage/spotlights/tv5.png` - TV channel logo
- `src/assets/homepage/spotlights/tv6.png` - TV channel logo

**Analysis:** These are media coverage images and logos that need to maintain clarity across devices.

### 5. Festival Gallery Images
**Current Usage:** Festival image layouts
- `src/assets/image-layouts/festival-gallery-1.jpg` through `festival-gallery-8.jpg`
- `src/assets/image-layouts/festival-main-1.jpg`
- `src/assets/image-layouts/festival-main-2.jpg`

**Analysis:** Gallery images for festival showcase.

### 6. Logo/Branding Images
**Current Usage:** Main logos and titles
- `src/assets/image-layouts/maintitlel.png` - Main title for laptop
- `src/assets/image-layouts/maintitlem.png` - Main title for mobile
- `src/assets/logo.png` - Main logo

**Analysis:** Brand elements that need to scale properly across devices.

### 7. Icons (Small UI Elements)
**Current Usage:** Small functional icons
- `src/assets/close.png` - Close button icon
- `src/assets/instrument.png` - Instrument icon
- `src/assets/locationicon.png` - Location icon

**Analysis:** Small icons that need optimization for different screen densities.

### 8. Social Media Icons
**Current Usage:** Social media links in footer
- `src/assets/social-media/facebook.png`
- `src/assets/social-media/instagram.png`
- `src/assets/social-media/linkedin.png`
- `src/assets/social-media/twitter-alt-circle.png`
- `src/assets/social-media/youtube.png`
- `src/assets/social-media/black_facebook.png`
- `src/assets/social-media/black-instagram.png`
- `src/assets/social-media/black-linkedIn.png`
- `src/assets/social-media/balck-X.png`

**Analysis:** Small social media icons that need consistent sizing.

### 9. Team Profile Images
**Current Usage:** Team member profiles
- `src/assets/team/1.png` through `src/assets/team/81.png`
- `src/assets/team/Sudha.jpg`
- `src/assets/team/Suresh-Chandra.jpg`

**Analysis:** Profile pictures that need responsive sizing for team cards.

### 10. School Activity Images
**Current Usage:** School program showcase
- `src/assets/schools/reconnect.jpg`
- `src/assets/schools/Interact with Animals.jpg`
- `src/assets/schools/Traditional Handicraft.jpg`
- `src/assets/schools/Tradtional Games.jpg`
- `src/assets/schools/Native Food.jpg`
- `src/assets/schools/Digital Detox.jpg`

**Analysis:** Activity showcase images for schools section.

### 11. School Posters
**Current Usage:** School program posters
- `src/assets/schools/school1.jpeg`
- `src/assets/schools/school2.jpeg`

**Analysis:** Poster images that need to maintain readability across devices.

### 12. Sponsor/Partner Logos
**Current Usage:** Footer sponsor display
- `src/assets/sponsors/footeLogo_1.png`
- `src/assets/sponsors/footeLogo_2.png`
- `src/assets/sponsors/fm_patner.png`
- `src/assets/sponsors/Ability_patner.png`
- `src/assets/sponsors/zero_food_waste patner.png`
- `src/assets/sponsors/media_patner.png`
- `src/assets/sponsors/dcc.png`
- `src/assets/sponsors/beauty_partner.png`
- `src/assets/sponsors/good_food_patner.png`
- `src/assets/sponsors/Knowledge Partner.png`

**Analysis:** Partner logos that need consistent sizing and clarity.

### 13. Website/Company Logos
**Current Usage:** Team member associated websites
- `src/assets/website-logo/nammaform.jpg`
- `src/assets/website-logo/thean.jpg`
- `src/assets/website-logo/millet.jpg`
- `src/assets/website-logo/Palmy.png`
- `src/assets/website-logo/thonadai.jpg`
- `src/assets/website-logo/nn.jpg`
- `src/assets/website-logo/amazon.jpg`
- `src/assets/website-logo/7.jpg`
- `src/assets/website-logo/<EMAIL>`
- `src/assets/website-logo/it-to-agri.jpg`

**Analysis:** Company/website logos displayed in team member details.

## Next Steps
1. Determine optimal sizes for each category based on device type
2. Copy new images from local system paths as specified in tasks.md
3. Create device-specific folder structure
4. Update code references for responsive image loading
