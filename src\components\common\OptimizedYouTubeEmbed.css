/* Optimized YouTube Embed Styles */
.optimized-youtube-embed {
  position: relative;
  width: 100%;
  height: 480px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.optimized-youtube-embed:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

/* YouTube iframe container */
.youtube-iframe-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 15px;
  overflow: hidden;
}

.youtube-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 15px;
  background: #000;
  transition: opacity 0.3s ease;
}

/* Loading placeholder */
.youtube-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.youtube-placeholder:hover {
  background: linear-gradient(135deg, #e3e7f0 0%, #b8c6db 100%);
  transform: scale(1.02);
}

.youtube-placeholder-content {
  text-align: center;
  color: #2c3e50;
}

.youtube-placeholder-icon {
  font-size: 3rem;
  color: #e67e22;
  margin-bottom: 15px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.05); }
}

.youtube-placeholder-content p {
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 20px;
  color: #34495e;
}

.youtube-load-button {
  background: linear-gradient(135deg, #e67e22, #d35400);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(230, 126, 34, 0.3);
}

.youtube-load-button:hover {
  background: linear-gradient(135deg, #d35400, #c0392b);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(230, 126, 34, 0.4);
}

/* Play overlay */
.youtube-play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 15px;
  z-index: 10;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.optimized-youtube-embed.playing .youtube-play-overlay {
  opacity: 0;
  pointer-events: none;
}

.youtube-play-button {
  background: rgba(255, 255, 255, 0.95);
  border: none;
  border-radius: 50px;
  padding: 20px 30px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.youtube-play-button:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.05);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

.youtube-play-icon {
  font-size: 1.5rem;
  color: #e67e22;
  margin-left: 3px; /* Slight offset to center the triangle */
}

/* Error state */
.youtube-error {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ff7675 0%, #fd79a8 100%);
  border-radius: 15px;
  color: white;
}

.youtube-error-content {
  text-align: center;
}

.youtube-error-content p {
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 15px;
}

.youtube-fallback-link {
  color: white;
  text-decoration: none;
  padding: 10px 20px;
  border: 2px solid white;
  border-radius: 25px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-block;
}

.youtube-fallback-link:hover {
  background: white;
  color: #e74c3c;
  transform: translateY(-2px);
}

/* Responsive design */
@media (max-width: 768px) {
  .optimized-youtube-embed {
    /* Fallback for browsers that don't support aspect-ratio */
    height: calc(100vw * 9 / 16 - 20px); /* 16:9 ratio minus margins */
    max-height: 220px; /* Prevent too large on wider mobile screens */
    /* Use aspect ratio to maintain 16:9 ratio and eliminate black bars */
    aspect-ratio: 16 / 9;
    height: auto; /* Override fallback when aspect-ratio is supported */
    border-radius: 12px;
    margin: 0 10px;
  }

  .youtube-iframe-container,
  .youtube-iframe,
  .youtube-placeholder,
  .youtube-error {
    border-radius: 12px;
  }

  .youtube-placeholder-icon {
    font-size: 2.2rem;
    margin-bottom: 10px;
  }

  .youtube-placeholder-content p {
    font-size: 0.95rem;
    margin-bottom: 12px;
  }

  .youtube-load-button {
    padding: 8px 18px;
    font-size: 0.85rem;
  }

  .youtube-play-button {
    padding: 12px 20px;
    font-size: 0.95rem;
    gap: 10px;
  }

  .youtube-play-icon {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .optimized-youtube-embed {
    /* Fallback for browsers that don't support aspect-ratio */
    height: calc(100vw * 9 / 16 - 10px); /* 16:9 ratio minus margins */
    max-height: 180px; /* Prevent too large on wider mobile screens */
    /* Use aspect ratio to maintain 16:9 ratio and eliminate black bars */
    aspect-ratio: 16 / 9;
    height: auto; /* Override fallback when aspect-ratio is supported */
    border-radius: 8px;
    margin: 0 5px;
  }

  .youtube-iframe-container,
  .youtube-iframe,
  .youtube-placeholder,
  .youtube-error {
    border-radius: 8px;
  }

  .youtube-placeholder-icon {
    font-size: 1.8rem;
    margin-bottom: 8px;
  }

  .youtube-placeholder-content p {
    font-size: 0.85rem;
    margin-bottom: 10px;
  }

  .youtube-load-button {
    padding: 6px 14px;
    font-size: 0.8rem;
  }

  .youtube-play-button {
    padding: 10px 16px;
    font-size: 0.85rem;
    gap: 8px;
  }

  .youtube-play-icon {
    font-size: 1.1rem;
  }
}

/* Extra small mobile devices */
@media (max-width: 360px) {
  .optimized-youtube-embed {
    /* Fallback for browsers that don't support aspect-ratio */
    height: calc(100vw * 9 / 16 - 4px); /* 16:9 ratio minus margins */
    max-height: 160px; /* Prevent too large on wider mobile screens */
    /* Use aspect ratio to maintain 16:9 ratio and eliminate black bars */
    aspect-ratio: 16 / 9;
    height: auto; /* Override fallback when aspect-ratio is supported */
    border-radius: 6px;
    margin: 0 2px;
  }

  .youtube-iframe-container,
  .youtube-iframe,
  .youtube-placeholder,
  .youtube-error {
    border-radius: 6px;
  }

  .youtube-placeholder-icon {
    font-size: 1.6rem;
    margin-bottom: 6px;
  }

  .youtube-placeholder-content p {
    font-size: 0.8rem;
    margin-bottom: 8px;
  }

  .youtube-load-button {
    padding: 5px 12px;
    font-size: 0.75rem;
  }

  .youtube-play-button {
    padding: 8px 14px;
    font-size: 0.8rem;
    gap: 6px;
  }

  .youtube-play-icon {
    font-size: 1rem;
  }
}

/* Performance optimizations */
.optimized-youtube-embed * {
  will-change: auto;
}

.optimized-youtube-embed.playing .youtube-iframe {
  will-change: transform;
}

/* Accessibility improvements */
.youtube-load-button:focus,
.youtube-play-button:focus {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .optimized-youtube-embed,
  .youtube-placeholder,
  .youtube-play-button,
  .youtube-load-button {
    transition: none;
  }
  
  .youtube-placeholder-icon {
    animation: none;
  }
}
