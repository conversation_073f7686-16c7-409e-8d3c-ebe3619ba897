/* Footer Styles */
.footer-container {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: white;
  margin-top: 60px;
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Primary Sponsors Section */
.primary-sponsors {
  display: flex;
  justify-content: center;
  gap: 80px;
  margin-bottom: 40px;
  padding: 20px 0;
}

.sponsor-item.primary {
  transform: scale(1.2);
}

.sponsor-item.primary .sponsor-logo {
  width: 100px;
  height: 100px;
  border: 4px solid #e67e22;
}

.sponsor-item.primary .sponsor-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
  background-color: white;
  border-radius: 50%;
}

.sponsor-item.primary .sponsor-name {
  font-size: 1.1rem;
  color: #ecf0f1;
  font-weight: 700;
  margin-top: 15px;
  padding: 0 10px;
}

.footer-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 30px 20px 0 20px;
  display: grid;
  grid-template-columns: 1.2fr 0.8fr;
  gap: 60px;
  align-items: start;
}

/* Contact Section */
.contact-section {
  padding-right: 20px;
}

.footer-heading {
  font-size: 1.6rem;
  font-weight: 700;
  color: #ecf0f1;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-bottom: 3px solid #0077ff;
  padding-bottom: 8px;
  display: inline-block;
}

.description-section {
  margin-bottom: 20px;
}

.footer-description {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #bdc3c7;
  text-align: justify;
  margin-bottom: 15px;
}

.contact-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.left-contact-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.right-company-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.location-details p {
  margin: 3px 0;
  color: #bdc3c7;
  font-size: 0.9rem;
}

.email-link {
  color: #DAA520; /* Traditional Gold */
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.email-link:hover {
  color: #00796B; /* Peacock Teal on hover */
  text-decoration: underline;
}

.phone-link {
  color: #DAA520; /* Traditional Gold */
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  display: block;
  margin-top: 5px;
}

.phone-link:hover {
  color: #00796B; /* Peacock Teal on hover */
  text-decoration: underline;
}

/* Social Media Styles */
.social-media-info {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.social-media-info .section-title {
  text-align: center;
}

/* Social media section styling */
.social-media-info .social-media-container {
  margin-top: 15px;
  justify-content: center;
}

.social-media-info .social-media-link {
  background: none;
  border: none;
  color: #ecf0f1;
}

.social-media-info .social-media-link:hover {
  background: none;
  transform: translateY(-3px) scale(1.1);
  box-shadow: none;
}

/* Partner Info Styles */
.partner-info {
  margin-top: 10px;
}

.partner-contact {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.partner-name {
  color: #ecf0f1;
  font-weight: 600;
  font-size: 0.95rem;
  margin: 0;
}

.partner-phone {
  color: #0077ff;
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  transition: color 0.3s ease;
}

.partner-phone:hover {
  color: #3498db;
  text-decoration: underline;
}

.gst-info {
  margin: 10px 0;
  color: #bdc3c7;
  font-size: 0.9rem;
}

.gst-label {
  font-weight: 600;
  margin-right: 8px;
}

.gst-number {
  font-family: 'Courier New', monospace;
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.company-name {
  font-weight: 600;
  color: #ecf0f1;
  margin: 10px 0;
  font-size: 1rem;
}

.address-info p {
  margin: 5px 0;
  color: #bdc3c7;
  font-size: 0.95rem;
}

/* Map Section */
.map-section {
  padding-left: 20px;
}

.map-container {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.map-container iframe {
  width: 100%;
  height: 250px;
  border: none;
  display: block;
}

/* Sponsors Section */
.sponsors-section {
  background: rgba(0, 0, 0, 0.1);
  padding: 40px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.sponsors-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ecf0f1;
  margin-bottom: 30px;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-bottom: 3px solid #0077ff;
  padding-bottom: 8px;
  display: inline-block;
}

.sponsors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 25px;
  max-width: 1400px;
  margin: 0 auto;
  justify-items: center;
  align-items: start;
  /* Optimized for up to 20 sponsors */
  grid-auto-rows: minmax(120px, auto);
}

.sponsor-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.3s ease, filter 0.3s ease;
}

.sponsor-item:hover {
  transform: translateY(-5px);
  filter: brightness(1.1);
}

.sponsor-logo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  border: 3px solid rgba(255, 255, 255, 0.1);
  transition: box-shadow 0.3s ease, border-color 0.3s ease;
}

.sponsor-logo:hover {
  box-shadow: 0 6px 20px rgba(0, 119, 255, 0.4);
  border-color: rgba(0, 119, 255, 0.5);
}

.sponsor-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
  background-color: white;
  border-radius: 50%;
}

.sponsor-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: #bdc3c7;
  margin: 0;
  text-align: center;
  letter-spacing: 0.5px;
}

/* Footer Bottom */
.footer-bottom {
  background: rgba(0, 0, 0, 0.2);
  text-align: center;
  padding: 15px 15px 50px 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 70px;
}

.footer-bottom p {
  margin: 0;
  color: #95a5a6;
  font-size: 0.9rem;
}

/* Large Screen Optimizations */
@media (min-width: 1400px) {
  .footer-content {
    max-width: 1600px;
    /* padding: 25px 40px; */
    gap: 80px;
  }

  .footer-heading {
    font-size: 1.7rem;
  }

  .map-container iframe {
    height: 280px;
  }

  .sponsors-title {
    font-size: 1.6rem;
  }

  .sponsors-grid {
    max-width: 1600px;
    gap: 35px;
    grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
  }

  .sponsor-logo {
    width: 85px;
    height: 85px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .primary-sponsors {
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }

  .sponsor-item.primary {
    transform: scale(1);
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 40px 15px;
  }

  .contact-section,
  .map-section {
    padding: 0;
  }

  .footer-heading {
    font-size: 1.5rem;
  }

  .footer-description {
    font-size: 0.95rem;
    line-height: 1.6;
  }

  .contact-details {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .left-contact-info {
    gap: 20px;
  }

  .partner-name {
    font-size: 0.9rem;
  }

  .partner-phone {
    font-size: 0.95rem;
  }

  .right-company-info {
    gap: 10px;
  }

  .section-title {
    font-size: 1rem;
  }

  .map-container iframe {
    height: 250px;
  }

  .sponsors-section {
    padding: 30px 15px;
  }

  .sponsors-title {
    font-size: 1.4rem;
    margin-bottom: 25px;
  }

  .sponsors-grid {
    grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
    gap: 20px;
    max-width: 100%;
  }

  .sponsor-logo {
    width: 65px;
    height: 65px;
  }

  .sponsor-name {
    font-size: 0.85rem;
  }

  /* Social media responsive */
  .social-media-info .social-media-container {
    justify-content: center;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .footer-content {
    padding: 0px 10px;
  }

  .footer-heading {
    font-size: 1.3rem;
    letter-spacing: 0.5px;
  }

  .footer-description {
    font-size: 0.9rem;
  }

  .section-title {
    font-size: 0.95rem;
  }

  .partner-name {
    font-size: 0.85rem;
  }

  .partner-phone {
    font-size: 0.9rem;
  }

  .location-details p,
  .address-info p {
    font-size: 0.9rem;
  }

  .company-name {
    font-size: 0.95rem;
  }

  .gst-info {
    font-size: 0.85rem;
  }

  .map-container iframe {
    height: 200px;
  }

  .footer-bottom {
    padding: 15px 15px 50px 15px;
    margin-bottom: 100px;
  }

  .footer-bottom p {
    font-size: 0.85rem;
  }

  .sponsors-section {
    padding: 25px 10px;
  }

  .sponsors-title {
    font-size: 1.2rem;
    margin-bottom: 20px;
  }

  .sponsors-grid {
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
    gap: 15px;
    max-width: 100%;
  }

  .sponsor-logo {
    width: 55px;
    height: 55px;
  }

  .sponsor-name {
    font-size: 0.8rem;
  }
}

/* Print Styles */
@media print {
  .footer-container {
    background: white !important;
    color: black !important;
  }

  .footer-heading,
  .section-title,
  .company-name {
    color: black !important;
  }

  .footer-description,
  .location-details p,
  .address-info p,
  .gst-info {
    color: #333 !important;
  }

  .map-container {
    display: none;
  }

  .email-link {
    color: #0077ff !important;
  }

  .partner-name {
    color: #333 !important;
  }

  .partner-phone {
    color: #0077ff !important;
  }

  .sponsors-section {
    background: white !important;
  }

  .sponsors-title {
    color: black !important;
  }

  .sponsor-name {
    color: #333 !important;
  }

  .sponsor-logo {
    border: 1px solid #ccc !important;
    box-shadow: none !important;
  }
}
