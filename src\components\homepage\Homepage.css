/* Custom Font Declarations - Based on Adobe Font List */
@font-face {
  font-family: 'AbrilFatface';
  src: url('../../assets/fonts/AbrilFatface-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Abuget';
  src: url('../../assets/fonts/Abuget.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Baloo2';
  src: url('../../assets/fonts/Baloo2-VariableFont_wght.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
  font-variation-settings: 'wght' 400;
}

@font-face {
  font-family: 'BebasKai';
  src: url('../../assets/fonts/BebasKai.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'BebasNeue';
  src: url('../../assets/fonts/BebasNeue-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Agamtoh';
  src: url('../../assets/fonts/Agamtoh.ttf') format('truetype'),
    url('../../assets/fonts/Agamtoh.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MinionPro-Bold';
  src: url('../../assets/fonts/MinionPro-Bold_0.otf') format('opentype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Poppins-BlackItalic';
  src: url('../../assets/fonts/Poppins-BlackItalic.ttf') format('truetype');
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

/* Pink Text Styling Component - Similar to the festival banner style */
.pink-text-banner {
  color: #e10887;
  background-color: white;
  border-radius: 40px;
  border: 1px solid black;
  padding: 15px 30px;
  display: inline-block;
  font-weight: bold;
  font-size: 1.2rem;
  text-align: center;
  margin: 10px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.pink-text-banner:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Responsive sizing for pink text banner */
@media (max-width: 768px) {
  .pink-text-banner {
    font-size: 1rem;
    padding: 12px 24px;
    border-radius: 30px;
  }
}

@media (min-width: 1920px) {
  .pink-text-banner {
    font-size: 1.4rem;
    padding: 18px 36px;
    border-radius: 45px;
  }
}

/* Homepage Styles */
.homepage-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 0px 0 200px;
  /* Added bottom padding for fixed countdown */
  text-align: center;
  animation: fadeIn 1s ease-in-out;
  width: 100%;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Main content */
.homepage-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 0;
  z-index: 2;
  position: relative;
}

/* Tagline section */
.tagline-section {
  margin-bottom: 40px;
  position: relative;
}

/* Tablet screens */
@media (min-width: 1024px) and (max-width: 1399px) {
  .preview-features {
    grid-template-columns: repeat(2, 1fr);
    max-width: 900px;
    gap: 20px;
  }

  .festival-tagline {
    white-space: normal; /* Allow text wrapping on tablet for double line */
    max-width: 80%;
    line-height: 1.3;
  }
}

/* Medium-large screens (1440p monitors) */
@media (min-width: 1400px) {
  .preview-features {
    max-width: 1300px;
    gap: 25px;
  }
}

/* Large screen optimizations for 24-inch monitors */
@media (min-width: 1920px) {

  /* Optimize preview section for large screens */
  .preview-features {
    max-width: 1400px;
    gap: 30px;
  }

  .preview-feature {
    padding: 25px;
  }

  /* Optimize snapshot sections for large screens */
  .snapshot-cards-2024,
  .snapshot-cards-2025 {
    max-width: 1600px;
  }

  .snapshot-row-1,
  .snapshot-row-2 {
    gap: 40px;
  }

  .snapshot-cards-2025 {
    gap: 40px;
  }

  .snapshot-image {
    height: 250px;
  }

  /* Increase height for first row cards on large screens */
  .snapshot-row-1 .snapshot-image {
    height: 320px;
  }

  .snapshot-text {
    padding: 30px;
    font-size: 1.2rem;
  }

  /* Optimize slideshow for large screens */
  .slideshow-container {
    max-width: 1400px;
    width: 90%;
  }

  .slide-image {
    height: 600px;
    object-fit: contain;
    object-position: center;
  }

  /* Print Media Images Large Screen Styles */
  .print-media-images {
    gap: 25px;
  }

  .print-media-image {
    height: 350px !important;
  }

  .recognition-grid {
    max-width: 1400px;
    gap: 40px;
  }

  .recognition-card {
    padding: 35px;
  }

  /* Full-width Print Media Large Screen Styles */
  .print-media-images-fullwidth {
    gap: 40px;
    padding: 0 40px;
  }

  .print-media-image-fullwidth {
    height: 600px !important;
    max-width: 800px;
  }

  .spotlight-subsection-title {
    font-size: 2.5rem;
    margin-bottom: 40px;
  }

  /* TV Channels Large Screen Card Styles */
  .tv-channels-cards-grid {
    gap: 50px;
    padding: 0 40px;
    max-width: 1600px;
  }

  .tv-channel-card {
    border-radius: 20px;
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
  }

  .tv-channel-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.25);
  }

  .tv-channel-image-container {
    height: 450px;
    padding: 15px;
  }

  /* Government Section Large Screen Card Styles */
  .government-cards-grid {
    padding: 0 40px;
    max-width: 1600px;
  }

  .government-card {
    border-radius: 20px;
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
    max-width: 900px;
  }

  .government-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.25);
  }

  .government-image-container {
    height: 500px;
    padding: 30px;
  }

  .government-card-info {
    padding: 35px;
  }

  .government-card-title {
    font-size: 1.6rem;
    margin-bottom: 15px;
  }

  .government-card-description {
    font-size: 1.1rem;
  }

  /* Government Large Screen Styles */
  .government-layout-new {
    gap: 40px;
    padding: 0 40px;
  }

  .government-sidebar-left {
    padding: 0;
  }

  .recognition-info {
    padding: 30px;
  }

  .government-sidebar-image {
    padding: 0 30px 30px 30px;
  }

  .government-sidebar-img {
    max-height: 200px;
  }

  /* Legacy styles */
  .government-layout {
    gap: 40px;
    padding: 0 40px;
  }

  .government-images-grid {
    gap: 30px;
  }

  .government-image-grid {
    height: 350px;
  }

  .government-sidebar {
    padding: 30px;
  }

  .ministry-title {
    font-size: 1.5rem;
  }

  .category-list li {
    font-size: 1rem;
    padding: 10px 0;
  }

  .slide-nav {
    font-size: 3rem;
    width: 70px;
    height: 70px;
    padding: 20px 25px;
  }

  .slide-nav.prev {
    left: 30px;
  }

  .slide-nav.next {
    right: 30px;
  }

  .dot {
    width: 16px;
    height: 16px;
  }
}

@keyframes logoGlow {
  from {
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));
  }

  to {
    filter: drop-shadow(0 8px 20px rgba(255, 215, 0, 0.4));
  }
}

/* Festival Tagline */
.festival-tagline {
  font-family: 'Poppins-BlackItalic', sans-serif;
  font-size: 1.5rem;
  font-weight: 900;
  font-style: italic;
  color: #e10887; /* Pink color as requested */
  background-color: white;
  border-radius: 40px;
  border: 1px solid black;
  text-align: center;
  margin: 20px auto;
  max-width: 900px; /* Further increased to ensure full text visibility */
  line-height: 1.3;
  letter-spacing: 0.5px;
  padding: 15px 35px; /* Increased horizontal padding */
  display: block; /* Changed from inline-block to block for better centering */
  width: fit-content; /* Only take up the space needed */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  white-space: nowrap; /* Prevent text wrapping to ensure single line display */
}

/* Tagline */
.tagline {
  font-size: 1.2rem;
  font-style: italic;
  color: #7f8c8d;
  margin-top: 15px;
  text-align: center;
  max-width: 500px;
  line-height: 1.4;
  white-space: pre-line;
}

/* Text content */
.text-content {
  color: #2c3e50;
  animation: slideInUp 1s ease-out 0.3s both;
  width: 100%;
  text-align: center; /* Ensure all child elements are centered */
  display: flex;
  flex-direction: column;
  align-items: center;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.main-title {
  font-size: 5rem;
  font-weight: 200;
  margin-bottom: 20px;
  color: #f6a323;
  /* Yellow color */
  letter-spacing: 2px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  width: 100%;
}

.title-logo-image {
  max-width: 100%;
  height: auto;
  /* Size similar to current text - approximately 4-5rem equivalent - increased by 10% */
  width: 550px;
  max-height: 195px;
  object-fit: contain;
  display: block;
  margin: 0 auto;
}



.subtitle {
  font-size: 5rem;
  font-weight: 550;
  margin-top: 30px;
  /* Updated subtitle color */
  line-height: 1.4;
  max-width: 100%;
  font-family: 'Abuget', sans-serif;
  /* Custom font for subtitle */
  word-spacing: 5px;
  color: #e95025;
  /* Updated subtitle color */
  text-shadow:
    -1px -1px 0 #ffffff,
    1px -1px 0 #ffffff,
    -1px 1px 0 #ffffff,
    1px 1px 0 #fffcfc,
    -2px 0 0 #ffffff,
    2px 0 0 #ffffff,
    0 -2px 0 #e6e1e1,
    0 2px 0 #e4d6d6;
  /* White outline */
}



.dates {
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  justify-content: flex-end;
  margin-right: 5px;
  /* White color */
  font-family: 'BebasNeue', sans-serif;
  /* Custom font for dates */
  letter-spacing: 1px;
  color: #ffffff;
  text-shadow:
    -1px -1px 0 #ffffff,
    1px -1px 0 #ffffff,
    -1px 1px 0 #ffffff,
    1px 1px 0 #fffcfc,
    -1px 0 0 #ffffff,
    1px 0 0 #ffffff,
    0 -2px 0 #e6e1e1,
    0 2px 0 #e4d6d6;
}

.venue {
  font-size: 1.3rem;
  margin-top: 0;
  display: flex;
  justify-content: flex-end;
  margin-right: 5px;
  letter-spacing: 1px;
  /* White color */
  font-family: 'BebasNeue', sans-serif;
  /* Custom font for venue */
  word-spacing: 2px;
  color: #ffffff;
  text-shadow:
    -1px -1px 0 #ffffff,
    1px -1px 0 #ffffff,
    -1px 1px 0 #ffffff,
    1px 1px 0 #fffcfc,
    -2px 0 0 #ffffff,
    2px 0 0 #ffffff,
    0 -2px 0 #e6e1e1,
    0 2px 0 #e4d6d6;
}

/* Countdown Timer Wrapper - Fixed at Bottom */
.countdown-wrapper {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  z-index: 10000 !important;
  /* Force hardware acceleration and prevent parent transform interference */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  will-change: transform;
  /* Ensure it's positioned relative to viewport, not parent */
  margin: 0 !important;
  padding: 0 !important;
  /* Prevent any parent container from affecting positioning */
  isolation: isolate;
  contain: layout style paint;
  /* Additional fixes to prevent scrolling with content */
  top: auto !important;
  position: fixed !important;
  /* Force it to be positioned relative to viewport */
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* Title above the golden border */
.countdown-title-wrapper {
  padding: 8px 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1px;
}

.countdown-title-icon {
  width: 32px;
  height: 32px;
  filter: brightness(0) invert(1);
  /* Makes the icon white */
}

.countdown-title {
  font-size: 1rem;
  color: #ffffff;
  font-family: 'BebasKai', sans-serif;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 2px;
}

/* Main countdown container with golden border */
.countdown-container {
  width: 100%;
  padding: 10px 40px 10px 40px;
  background: #0a7a64;
  border-top: 6px solid #f3a41d;
  border-radius: 0;
  backdrop-filter: none;
  box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.4);
  opacity: 1;
  visibility: visible;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  margin: 0;
}

/* Left side - Date information */
.countdown-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #ffffff;
  font-family: 'BebasNeue', sans-serif;
}

.countdown-dates {
  font-size: 1.2rem;
  margin-bottom: 5px;
  letter-spacing: 2px;
  font-family: 'BebasKai', sans-serif;
  color: #ffffff;
}

.countdown-tamil-dates {
  font-size: 1rem;
  font-weight: 500;
  opacity: 0.9;
  letter-spacing: 0.5px;
}

/* Center - Timer section */
.countdown-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}



/* Right side - Location information */
.countdown-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-family: 'BebasNeue', sans-serif;
}

.countdown-location {
  display: flex;
  align-items: center;
  gap: 8px;
}

.location-icon {
  width: 1.3rem;
  height: 1.3rem;
  flex-shrink: 0;
  object-fit: contain;
}

.location-text {
  font-size: 1.1rem;
  letter-spacing: 2px;
  line-height: 1.2;
  font-family: 'BebasKai', sans-serif;
  color: #ffffff;
}

/* Desktop Pre-registration Button - Main Content Area */
.desktop-preregister-main {
  margin-top: 25px;
  display: flex;
  justify-content: center;
  width: 100%;
}

.desktop-preregister-button {
  padding: 12px 28px; /* Larger padding for main content area */
  font-size: 1.1rem; /* Larger font for prominence */
  font-weight: 700; /* Bold for desktop */
  letter-spacing: 1px; /* More letter spacing for desktop */
  border-radius: 10px; /* Larger radius for desktop */
  box-shadow: 0 5px 15px rgba(218, 165, 32, 0.35); /* Stronger shadow for desktop */
  border: 2px solid transparent; /* Thicker border for desktop */
  text-transform: uppercase;
  background: linear-gradient(135deg, #DAA520, #B8860B); /* Traditional Gold gradient */
  color: #ffffff;
  text-decoration: none;
  transition: all 0.3s ease;
}

.desktop-preregister-button:hover {
  background: linear-gradient(135deg, #B8860B, #9A7209);
  transform: translateY(-3px); /* More lift on desktop hover */
  box-shadow: 0 8px 25px rgba(218, 165, 32, 0.45);
  border: 2px solid #DAA520;
  color: #ffffff;
  text-decoration: none;
}

.desktop-preregister-button:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(218, 165, 32, 0.35);
}

/* Default: Hide mobile date and location (only show on mobile) */
.homepage-container .mobile-date-location {
  display: none;
  visibility: hidden;
  opacity: 0;
}

/* Show mobile date and location on small screens */
@media (max-width: 768px) {
  .homepage-container .mobile-date-location {
    display: flex !important;
    flex-direction: column;
    align-items: center;
    margin-top: 15px;
    gap: 5px;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .mobile-dates {
    font-size: 1.1rem;
    font-family: 'BebasKai', sans-serif;
    font-weight: 700;
    letter-spacing: 2px;
    color: #000000;
    text-shadow:
      -1px -1px 0 #ffffff,
      1px -1px 0 #ffffff,
      -1px 1px 0 #ffffff,
      1px 1px 0 #fffcfc,
      -2px 0 0 #ffffff,
      2px 0 0 #ffffff,
      0 -2px 0 #e6e1e1,
      0 2px 0 #e4d6d6;
  }

  .mobile-venue {
    font-size: 1rem;
    font-family: 'BebasKai', sans-serif;
    font-weight: 700;
    letter-spacing: 1px;
    color: #000000;
    text-shadow:
      -1px -1px 0 #ffffff,
      1px -1px 0 #ffffff,
      -1px 1px 0 #ffffff,
      1px 1px 0 #fffcfc,
      -2px 0 0 #ffffff,
      2px 0 0 #ffffff,
      0 -2px 0 #e6e1e1,
      0 2px 0 #e4d6d6;
  }

  .mobile-preregister {
    margin-top: 12px;
  }

  .preregister-button {
    display: inline-block;
    padding: 8px 16px; /* Reduced padding for mobile */
    background: linear-gradient(135deg, #DAA520, #B8860B); /* Traditional Gold gradient */
    color: #ffffff;
    text-decoration: none;
    border-radius: 6px; /* Slightly smaller radius */
    font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 600; /* Reduced font weight */
    font-size: 0.85rem; /* Smaller font size for mobile */
    text-transform: uppercase;
    letter-spacing: 0.5px; /* Reduced letter spacing */
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(218, 165, 32, 0.25); /* Reduced shadow */
    border: 1px solid transparent; /* Thinner border */
  }

  .preregister-button:hover {
    background: linear-gradient(135deg, #B8860B, #9A7209);
    transform: translateY(-1px); /* Reduced hover lift */
    box-shadow: 0 4px 15px rgba(218, 165, 32, 0.35);
    border: 1px solid #DAA520;
    color: #ffffff;
    text-decoration: none;
  }

  .preregister-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(218, 165, 32, 0.25);
  }

  /* Hide desktop pre-registration button on mobile */
  .desktop-preregister-main {
    display: none !important;
  }
}

/* Desktop layout - show all three sections */
@media (min-width: 769px) {
  .countdown-container {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: space-between !important;
    gap: 0 !important;
  }

  .countdown-left,
  .countdown-right {
    display: flex !important;
  }

  .countdown-center {
    flex: 1 !important;
  }

  /* Hide mobile date and location on desktop */
  .homepage-container .mobile-date-location {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }

  /* Hide mobile pre-registration button on desktop */
  .mobile-preregister {
    display: none !important;
  }

  /* Show desktop pre-registration button on desktop */
  .desktop-preregister-main {
    display: flex !important;
  }
}

.countdown-timer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  width: 100%;
}

/* Top section - Numbers only */
.countdown-numbers-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 35px;
  flex-wrap: wrap;
}

.countdown-item {
  display: flex;
  align-items: center;
  position: relative;
}

.countdown-number {
  font-size: 2rem;
  font-weight: 800;
  color: #ffffff;
  /* White color */
  text-shadow: none;
  background: none;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: unset;
  background-clip: unset;
  font-family: 'Baloo2', sans-serif;
  line-height: 1;
  margin: 0;
}

.countdown-separator {
  font-size: 2rem;
  font-weight: 800;
  color: #ffffff;
  /* White color */
  text-shadow: none;
  animation: none;
  line-height: 1;
  margin: 0 5px;
}

/* Horizontal divider line */
.countdown-divider {
  width: 70%;
  height: 2px;
  background-color: #000000;
  margin: 1px 0;
}

/* Bottom section - Labels only */
.countdown-labels-section {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.countdown-label-item {
  min-width: 70px;
  text-align: center;
}

.countdown-label {
  font-size: 0.8rem;
  font-weight: 500;
  color: #ffffff;
  /* White color */
  text-transform: uppercase;
  letter-spacing: 1px;
  margin: 0;
  text-shadow: none;
  line-height: 1;
  font-family: 'Baloo2', sans-serif;
}

/* Seconds visibility is now controlled by JavaScript for proper synchronization */

/* iPhone 12/13/14 and similar small mobile screens (390x844) */
@media (max-width: 430px) and (min-height: 800px) {
  .countdown-wrapper {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100vw !important;
    height: auto !important;
    z-index: 10003 !important;
    /* Force viewport positioning for iPhone sizes */
    -webkit-transform: translate3d(0, 0, 0) !important;
    transform: translate3d(0, 0, 0) !important;
    will-change: transform !important;
    /* Prevent any parent interference */
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    /* Force hardware acceleration */
    isolation: isolate !important;
    contain: layout style paint !important;
    /* Ensure it's positioned relative to viewport */
    margin: 0 !important;
    padding: 0 !important;
    /* Force layer creation for iPhone screens */
    -webkit-perspective: 1000px !important;
    perspective: 1000px !important;
    -webkit-transform-style: preserve-3d !important;
    transform-style: preserve-3d !important;
    /* Additional iPhone-specific fixes */
    -webkit-overflow-scrolling: touch !important;
    /* Force it to stay at bottom */
    top: auto !important;
    bottom: 0px !important;
  }
}

/* Small mobile screens general fixes (390x844 and similar) */
@media (max-width: 430px) and (max-height: 900px) {
  .countdown-wrapper {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100vw !important;
    z-index: 10001 !important;
    /* Force viewport positioning */
    -webkit-transform: translate3d(0, 0, 0) !important;
    transform: translate3d(0, 0, 0) !important;
    will-change: transform !important;
    /* Prevent any parent interference */
    position: fixed !important;
    /* Force hardware acceleration */
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    /* Ensure it's positioned relative to viewport */
    margin: 0 !important;
    padding: 0 !important;
    /* Force layer creation */
    isolation: isolate !important;
    contain: layout style paint !important;
    /* Additional small screen fixes */
    -webkit-perspective: 1000px !important;
    perspective: 1000px !important;
    -webkit-transform-style: preserve-3d !important;
    transform-style: preserve-3d !important;
  }
}

/* iOS-specific fixes for countdown wrapper */
@supports (-webkit-touch-callout: none) {
  .countdown-wrapper {
    /* Force hardware acceleration on iOS */
    position: fixed !important;
    bottom: 0 !important;
    z-index: 10001 !important; /* Highest z-index for iOS */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
    /* iOS Safari specific positioning fixes */
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    /* Prevent iOS scroll interference */
    -webkit-overflow-scrolling: touch;
    /* Force hardware acceleration and prevent parent transform interference */
    isolation: isolate;
    contain: layout style paint;
    /* Ensure it's positioned relative to viewport, not parent */
    margin: 0 !important;
    padding: 0 !important;
    /* Additional iOS-specific fixes */
    -webkit-perspective: 1000px;
    perspective: 1000px;
    /* Force layer creation */
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
}

@keyframes numberPulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

/* Video Section Styles */
.video-section {
  margin-top: 80px;
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 40px;
}

.video-title {
  font-size: 2rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
  text-align: center;
}

.single-video-container {
  position: relative;
  padding-bottom: 56.25%;
  /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0.05);
}

.youtube-video-main {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 15px;
  border: none;
  outline: none;
}

/* Video overlay for play button */
.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 15px;
  z-index: 10;
}

.video-play-overlay-btn {
  background: rgba(255, 255, 255, 0.95);
  border: none;
  border-radius: 50px;
  padding: 20px 30px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.video-play-overlay-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.05);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

.play-icon-large {
  font-size: 1.5rem;
  color: #e67e22;
}

/* YouTube Video Embed Styles - Full Width Horizontal Layout */
.dual-video-container {
  margin-top: 40px;
  width: 100%;
  max-width: 100%;
  display: flex;
  gap: 30px;
  justify-content: center;
  align-items: center;
  padding: 0 40px;
}

.video-item {
  flex: 1;
  max-width: none;
  /* Remove max-width constraint for full width */
  position: relative;
  padding-bottom: 28.125%;
  /* More horizontal aspect ratio (16:9 reduced to ~16:4.5) */
  height: 0;
  overflow: hidden;
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  background: rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: block;
  /* Ensure both videos are visible on desktop */
}

.video-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.6);
}

.youtube-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 15px;
  border: none;
  outline: none;
}

/* Ensure both videos are visible on larger screens */
@media (min-width: 769px) {
  .video-item:nth-child(2) {
    display: block;
  }
}

/* Video play button */
.video-play-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  /* box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3); */
  z-index: 10;
}

.video-play-button:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.play-icon {
  margin-left: 3px;
}

/* Section Headings with Tamil Cultural Styling */
.section-heading {
  font-size: 2.5rem;
  font-weight: 700;
  color: #4B0000;
  /* Deep Maroon */
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  text-align: center;
  margin-bottom: 40px;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
}

/* Snapshot Sections */
.snapshot-section {
  padding: 40px 20px;
  background: rgba(248, 246, 240, 0.3);
  margin-bottom: 40px;
}

/* 2024 Snapshot Cards Layout */
.snapshot-cards-2024 {
  max-width: 1400px;
  margin: 0 auto;
}

.snapshot-row-1 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  margin-bottom: 30px;
}

.snapshot-row-2 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

/* 2025 Snapshot Cards Layout */
.snapshot-cards-2025 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Image-based Snapshot Cards */
.snapshot-image-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.snapshot-image-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.snapshot-image-card.new-feature {
  transform: scale(1.1);
  border: 3px solid #e67e22;
}

.snapshot-image-card.new-feature:hover {
  transform: scale(1.1) translateY(-8px);
}

.new-tag {
  position: absolute;
  top: 15px;
  right: 15px;
  background: #e74c3c;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 700;
  z-index: 2;
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.snapshot-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

/* Increase height for first row cards (first 2 cards) */
.snapshot-row-1 .snapshot-image {
  height: 280px;
}

.snapshot-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.snapshot-image-card:hover .snapshot-image img {
  transform: scale(1.05);
}

.snapshot-text {
  padding: 25px;
  text-align: center;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333333;
  /* Tamil cultural body text */
  font-family: 'Inter', 'Open Sans', 'Poppins', sans-serif;
  line-height: 1.5;
}

/* Vision Section */
.vision-section {
  width: 100%;
  padding: 40px 0;
  background: rgba(248, 246, 240, 0.5);
}

.vision-content {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}

.vision-main-row {
  display: flex;
  align-items: center;
  gap: 60px;
  max-width: 1400px;
  margin: 0 auto;
}

.vision-main-image {
  flex: 1.2;
  max-width: 700px;
}

.vision-text-content {
  flex: 1;
  color: #2c3e50;
  text-align: left;
  padding: 0 20px;
}

/* Slideshow Section */
.slideshow-section {
  padding: 50px 20px;
  width: 100%;
}

.slideshow-container {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25);
  width: 95%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.slideshow-wrapper {
  display: flex;
  transition: transform 0.5s ease-in-out;
  width: 100%;
  height: 100%;
}

.slide {
  min-width: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.slide-image {
  width: 100%;
  height: 500px;
  object-fit: contain;
  object-position: center;
  display: block;
  margin: 0 auto;
}

.slide-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  font-size: 2.5rem;
  padding: 15px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide-nav:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: translateY(-50%) scale(1.1);
}

.slide-nav.prev {
  left: 20px;
}

.slide-nav.next {
  right: 20px;
}

.slide-dots {
  position: absolute;
  bottom: 25px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  z-index: 10;
}

.dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(255, 255, 255, 1);
  transform: scale(1.2);
}

.dot:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(255, 255, 255, 1);
}

/* Image Layout Sections */
.image-layout-section {
  margin-top: 60px;
  width: 100%;
  padding: 40px 0;
  background: rgba(248, 246, 240, 0.5);
}

.layout-content {
  width: 100%;
  max-width: 100%;
  padding: 0 40px;
}

.layout-main-row {
  display: flex;
  align-items: center;
  gap: 60px;
  margin-bottom: 40px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.layout-main-row.reverse {
  flex-direction: row-reverse;
}

.layout-main-image {
  flex: 1;
  max-width: 600px;
}

.main-layout-img {
  width: 100%;
  height: 500px;
  object-fit: cover;
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}

.main-layout-img:hover {
  transform: scale(1.02);
}

.layout-text-content {
  flex: 1;
  color: #333333;
  /* Tamil cultural body text */
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  text-align: left;
  padding: 0 20px;
  line-height: 1.7;
}

.layout-heading {
  font-size: 2.8rem;
  font-weight: 700;
  color: #4B0000;
  /* Deep Maroon */
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  margin-bottom: 25px;
  line-height: 1.2;
  letter-spacing: 0.8px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
}

.layout-description {
  font-size: 1.2rem;
  line-height: 1.7;
  color: #000000;
  margin: 0;
  white-space: pre-line;
  text-align: justify;
}

.layout-small-images {
  display: flex;
  gap: 30px;
  justify-content: center;
  flex-wrap: wrap;
  max-width: 1400px;
  margin: 0 auto;
  justify-content: space-between;
}

.small-image-item {
  flex: 1;
  min-width: 250px;
  max-width: 320px;
}

.small-layout-img {
  width: 100%;
  height: 220px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.small-layout-img:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 36px rgba(0, 0, 0, 0.4);
}

/* Spotlight Section */
.spotlight-section {
  padding: 40px 0;
  background: rgba(248, 246, 240, 0.3);
}

/* Recognition Section (for remaining cards) */
.recognition-section {
  padding: 40px 20px;
  background: rgba(248, 246, 240, 0.3);
}

.recognition-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1000px;
  margin: 0 auto;
}

.recognition-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  padding: 25px;
  text-align: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.recognition-type {
  font-size: 1.4rem;
  font-weight: 600;
  color: #e67e22;
  margin-bottom: 15px;
}

.recognition-outlets {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recognition-outlets li {
  padding: 5px 0;
  color: #2c3e50;
  font-weight: 500;
}

/* Print Media Images Styles */
.print-media-images {
  display: flex;
  gap: 15px;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.print-media-image {
  flex: 1;
  width: 100%;
  height: 280px !important;
  object-fit: cover;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.print-media-image:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Full-width Print Media Section */
.print-media-section {
  width: 100%;
  margin-bottom: 40px;
}

.spotlight-subsection-title {
  font-size: 2rem;
  font-weight: 700;
  color: #4B0000;
  /* Deep Maroon - matching schools page */
  text-align: center;
  margin-bottom: 30px;
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
}

.print-media-images-fullwidth {
  display: flex;
  gap: 20px;
  width: 100%;
  max-width: 100%;
  padding: 0 20px;
  justify-content: center;
}

.print-media-image-fullwidth {
  flex: 1;
  width: 100%;
  max-width: 700px;
  height: 500px !important;
  object-fit: contain;
  object-position: center;
  border-radius: 15px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.print-media-image-fullwidth:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

/* TV Channels Section - Card-based Layout */
.tv-channels-section {
  width: 100%;
  margin-bottom: 40px;
}

.tv-channels-cards-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, auto);
  gap: 30px;
  width: 100%;
  padding: 0 20px;
  max-width: 1400px;
  margin: 0 auto;
  justify-items: stretch;
  align-items: stretch;
}

.tv-channel-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.tv-channel-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.tv-channel-link {
  display: block;
  text-decoration: none;
  width: 100%;
  height: 100%;
}

.tv-channel-image-container {
  width: 100%;
  height: 400px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
}

.tv-channel-card-image {
  border-radius: 15px;
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
  transition: transform 0.3s ease;
}

.tv-channel-card:hover .tv-channel-card-image {
  transform: scale(1.05);
}

/* Government Section - Card-based Layout */
.government-section {
  width: 100%;
  margin-bottom: 40px;
}

.government-cards-grid {
  display: flex;
  justify-content: center;
  width: 100%;
  padding: 0 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.government-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  max-width: 800px;
  width: 100%;
}

.government-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.government-card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.government-image-container {
  width: 100%;
  height: 450px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
}

.government-card-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
  transition: transform 0.3s ease;
}

.government-card:hover .government-card-image {
  transform: scale(1.05);
}

.government-card-info {
  padding: 25px;
  text-align: center;
  background: white;
}

.government-card-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: #4B0000;
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  margin-bottom: 10px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.government-card-description {
  font-size: 1rem;
  color: #333333;
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.6;
  margin: 0;
}

.print-media-images-fullwidth a {
  display: block;
  text-decoration: none;
  width: 100%;
  border-radius: 12px;
  transition: transform 0.2s ease;
}

.print-media-images-fullwidth a:active {
  transform: scale(0.98);
}

.print-media-image-fullwidth {
  cursor: pointer;
}

.recognition-info {
  text-align: left;
}

.recognition-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
}

.ministry-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #4B0000;
  margin-bottom: 20px;
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
}

.category-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-list li {
  padding: 8px 0;
  font-size: 0.9rem;
  font-weight: 500;
  color: #555;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.category-list li:last-child {
  border-bottom: none;
}

/* Government Section - New Layout */
.government-section {
  width: 100%;
  margin-bottom: 40px;
}

.government-layout-new {
  display: flex;
  gap: 30px;
  width: 100%;
  padding: 0 20px;
  height: 550px;
  max-width: 1400px;
  margin: 0 auto;
  justify-content: center;
  align-items: center;
}

.government-sidebar-left {
  flex: 0 0 20%;
  width: 20%;
  height: 550px;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.recognition-info {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.government-sidebar-image {
  flex: 0 0 auto;
  padding: 0 20px 20px 20px;
}

.government-sidebar-img {
  width: 100%;
  height: auto;
  max-height: 180px;
  object-fit: cover;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.government-main-right {
  flex: 0 0 80%;
  width: 80%;
  height: 550px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.government-main-img {
  width: 100%;
  height: 550px;
  object-fit: contain;
  object-position: center;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.government-main-img:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

/* Legacy styles for backward compatibility */
.government-layout {
  display: flex;
  gap: 30px;
  width: 100%;
  padding: 0 20px;
}

.government-main {
  flex: 0 0 80%;
  width: 80%;
}

.government-images-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  width: 100%;
}

.government-image-grid {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.government-image-grid:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.government-sidebar {
  flex: 0 0 20%;
  width: 20%;
  padding: 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  height: fit-content;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

/* Preview Section */
.preview-section {
  padding: 40px 20px;
}

.preview-features {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.preview-feature {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.preview-feature:hover {
  transform: translateY(-3px);
}

.feature-icon {
  font-size: 1.5rem;
}

.feature-text {
  font-size: 1.1rem;
  color: #2c3e50;
  font-weight: 500;
}

/* Testimonials Section */
.testimonials-section {
  padding: 50px 40px;
  background: rgba(248, 246, 240, 0.3);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  width: 100%;
  margin: 0 auto;
  max-width: 1200px;
}

/* For very large screens (24+ inch monitors) - 4 columns in single row */
@media (min-width: 1600px) {
  .testimonials-section {
    padding: 60px 60px;
  }

  .testimonials-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
    max-width: none;
  }
}

/* For large desktop screens (1200px-1600px) - 2x2 grid */
@media (min-width: 1200px) and (max-width: 1599px) {
  .testimonials-section {
    padding: 50px 40px;
  }

  .testimonials-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 35px;
    max-width: 1200px;
  }
}

/* For tablets and smaller laptops */
@media (max-width: 1199px) {
  .testimonials-section {
    padding: 40px 30px;
  }

  .testimonials-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
    max-width: 900px;
  }
}

/* For mobile screens */
@media (max-width: 768px) {
  .testimonials-section {
    padding: 30px 20px;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 25px;
  }
}

.testimonial-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 35px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(230, 126, 34, 0.1);
  min-height: 280px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Enhanced styling for large desktop screens (2x2 layout) */
@media (min-width: 1200px) and (max-width: 1599px) {
  .testimonial-card {
    padding: 40px;
    min-height: 320px;
  }
}

/* Enhanced styling for very large screens (4 columns) */
@media (min-width: 1600px) {
  .testimonial-card {
    padding: 45px;
    min-height: 380px;
  }

  .testimonial-text {
    font-size: 1.1rem;
  }

  .testimonial-author {
    font-size: 1rem;
  }
}

.testimonial-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: 15px;
  left: 25px;
  font-size: 4rem;
  color: rgba(230, 126, 34, 0.2);
  font-family: Georgia, serif;
  line-height: 1;
}

.testimonial-text {
  font-size: 1rem;
  line-height: 1.7;
  color: #2c3e50;
  font-style: italic;
  margin-bottom: 25px;
  margin-top: 20px;
  flex-grow: 1;
  text-align: justify;
}

.testimonial-author {
  font-size: 0.9rem;
  color: #e67e22;
  font-weight: 700;
  text-align: right;
  margin: 0;
  padding-top: 15px;
  border-top: 2px solid rgba(230, 126, 34, 0.2);
  line-height: 1.4;
}

/* Medium screens (tablets and small laptops) */
@media (max-width: 1024px) and (min-width: 769px) {
  .slideshow-container {
    max-width: 900px;
    width: 90%;
  }

  .slide-image {
    height: 450px;
    object-fit: contain;
    object-position: center;
  }

  .slide-nav {
    font-size: 2.2rem;
    width: 55px;
    height: 55px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .homepage-container {
    padding: 6px 0 180px;
    /* Added bottom padding for fixed countdown, adjusted for new navbar height */
    width: 100%;
  }

  .main-logo {
    max-width: 300px;
  }

  .main-title {
    font-size: 5rem;
    letter-spacing: 1px;
    color: #f6a323;
    /* Yellow color */
    position: relative;
    display: inline-block;
  }

  .title-logo-image {
    width: 385px; /* Increased by 10% from 350px */
    max-height: 110px; /* Increased by 10% from 100px */
  }

  .festival-tagline {
    font-size: 1.1rem;
    margin: 15px auto;
    max-width: 90%;
    line-height: 1.2;
    white-space: normal; /* Allow text wrapping on mobile for double line */
  }



  .subtitle {
    display: flex;
    justify-content: end;
    font-size: 1.4rem;
    font-weight: 550;
    margin-top: 15px;
    /* Updated subtitle color */
    font-family: 'Abuget', sans-serif;
    /* Custom font for subtitle */
    word-spacing: 5px;
    color: #e95025;
    /* Updated subtitle color */
    text-shadow:
      -1px -1px 0 #ffffff,
      1px -1px 0 #ffffff,
      -1px 1px 0 #ffffff,
      1px 1px 0 #fffcfc,
      -2px 0 0 #ffffff,
      2px 0 0 #ffffff,
      0 -2px 0 #e6e1e1,
      0 2px 0 #e4d6d6;
  }

  .dates {
    font-size: 1.2rem;
    display: flex;
    justify-content: end;
    /* White color */
    font-family: 'BebasNeue', sans-serif;
    /* Custom font for dates */
    color: #ffffff;
    letter-spacing: 1px;
    text-shadow:
      -1px -1px 0 #ffffff,
      1px -1px 0 #ffffff,
      -1px 1px 0 #ffffff,
      1px 1px 0 #fffcfc,
      -1px 0 0 #ffffff,
      1px 0 0 #ffffff,
      0 -2px 0 #e6e1e1,
      0 2px 0 #e4d6d6;
  }

  .venue {
    font-size: 1.1rem;
    /* White color */
    font-family: 'BebasNeue', sans-serif;
    /* Custom font for venue */
    word-spacing: 2px;
    letter-spacing: 1px;

    color: #ffffff;
    text-shadow:
      -1px -1px 0 #ffffff,
      1px -1px 0 #ffffff,
      -1px 1px 0 #ffffff,
      1px 1px 0 #fffcfc,
      -2px 0 0 #ffffff,
      2px 0 0 #ffffff,
      0 -2px 0 #e6e1e1,
      0 2px 0 #e4d6d6;
  }

  /* Countdown responsive - FORCE FIXED at Bottom for Tablets */
  .countdown-wrapper {
    position: fixed !important;
    bottom: 0px !important;
    left: 0px !important;
    right: 0px !important;
    width: 100vw !important;
    height: auto !important;
    z-index: 99999 !important;
    /* FORCE viewport positioning for tablets */
    -webkit-transform: translate3d(0, 0, 0) !important;
    transform: translate3d(0, 0, 0) !important;
    will-change: transform !important;
    /* FORCE hardware acceleration */
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    /* PREVENT parent interference */
    isolation: isolate !important;
    contain: layout style paint !important;
    /* ENSURE viewport positioning */
    margin: 0 !important;
    padding: 0 !important;
    /* FORCE layer creation */
    -webkit-perspective: 1000px !important;
    perspective: 1000px !important;
    -webkit-transform-style: preserve-3d !important;
    transform-style: preserve-3d !important;
    /* TABLET SPECIFIC - prevent scrolling with content */
    position: fixed !important;
    top: auto !important;
    bottom: 0px !important;
  }

  .countdown-title-wrapper {
    padding: 6px 0;
  }

  .countdown-title-icon {
    width: 28px;
    height: 28px;
  }

  .countdown-title {
    font-size: 1rem;
  }

  .countdown-container {
    padding: 12px 15px 10px 15px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
  }

  .countdown-left,
  .countdown-right {
    display: flex; /* Show on tablet */
    flex: 0 0 auto;
    min-width: 0;
  }

  .countdown-left {
    text-align: left;
    align-items: flex-start;
    padding-left: 8px;
  }

  .countdown-right {
    text-align: right;
    align-items: flex-end;
    padding-right: 8px;
  }

  .countdown-dates {
    font-size: 0.9rem !important;
    margin-bottom: 3px !important;
    letter-spacing: 1.5px !important;
    line-height: 1.2 !important;
    color: #000000 !important;
  }

  .countdown-location {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-end !important;
    gap: 3px !important;
  }

  .location-icon {
    width: 16px !important;
    height: 16px !important;
    margin-bottom: 2px !important;
  }

  .location-text {
    font-size: 0.9rem !important;
    letter-spacing: 1px !important;
    line-height: 1.2 !important;
    color: #000000 !important;
    text-align: right !important;
  }

  .countdown-center {
    flex: 1;
    width: auto;
    display: flex;
    justify-content: center;
  }

  /* Hide mobile date and location on tablet */
  .homepage-container .mobile-date-location {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }

  .countdown-timer {
    gap: 12px;
  }

  .countdown-numbers-section {
    gap: 20px;
  }

  .countdown-number {
    font-size: 2.5rem;
    color: #ffffff;
    /* White color */
    margin: 0;
    font-family: 'Baloo2', sans-serif;
  }

  .countdown-separator {
    font-size: 2rem;
    color: #ffffff;
    /* White color */
    margin: 0 5px;
  }

  .countdown-divider {
    margin: 6px 0;
  }

  .countdown-labels-section {
    gap: 1px;
  }

  .countdown-label-item {
    min-width: 50px;
  }

  .countdown-label {
    font-size: 0.8rem;
    color: #ffffff;
    /* White color */
    margin: 0;
    font-family: 'Baloo2', sans-serif;
  }

  /* Video responsive */
  .video-section {
    margin-top: 30px;
    padding: 0 20px;
  }

  .video-title {
    font-size: 1.6rem;
    margin-bottom: 15px;
  }

  /* Video responsive - Single video for mobile */
  .dual-video-container {
    margin-top: 30px;
    flex-direction: column;
    gap: 20px;
    padding: 0 20px;
    justify-content: center;
    align-items: center;
    width: 100%;
    display: flex;
  }

  .video-item {
    width: 100%;
    max-width: 100%;
    padding-bottom: 56.25%;
    /* Standard 16:9 aspect ratio for mobile */
    border-radius: 12px;
    position: relative;
    height: 0;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.1);
    display: block;
  }

  /* Hide second video on mobile */
  .video-item:nth-child(2) {
    display: none;
  }

  .video-item:hover {
    transform: none;
    /* Disable hover effects on mobile */
  }

  .youtube-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 12px;
    border: none;
  }

  .video-play-button {
    width: 50px;
    height: 50px;
    bottom: 20px;
    right: 20px;
  }

  /* Mobile video overlay styles */
  .video-play-overlay-btn {
    padding: 15px 20px;
    font-size: 1rem;
    gap: 10px;
  }

  .play-icon-large {
    font-size: 1.2rem;
  }

  /* Image Layout responsive */
  .image-layout-section {
    margin-top: 40px;
    padding: 30px 0;
  }

  .layout-content {
    padding: 0 20px;
  }

  .layout-main-row {
    flex-direction: column;
    gap: 25px;
    margin-bottom: 25px;
    max-width: 100%;
  }

  .layout-main-row.reverse {
    flex-direction: column-reverse;
  }

  .layout-main-image {
    max-width: 100%;
  }

  .main-layout-img {
    height: 280px;
    border-radius: 12px;
  }

  .layout-text-content {
    text-align: center;
    padding: 0 10px;
  }

  .layout-heading {
    font-size: 2rem;
    margin-bottom: 15px;
  }

  .layout-description {
    font-size: 1rem;
    line-height: 1.5;
  }

  .layout-small-images {
    gap: 15px;
    max-width: 100%;
    padding: 0 10px;
  }

  .small-image-item {
    min-width: 180px;
    max-width: 220px;
  }

  .small-layout-img {
    height: 160px;
    border-radius: 10px;
  }
}

@media (max-width: 480px) {

  /* Global mobile optimizations */
  * {
    box-sizing: border-box;
  }

  body {
    margin: 0;
    padding: 0;
  }

  .main-title {
    font-size: 2.5rem;
    letter-spacing: 1px;
    color: #f6a323;
    /* Yellow color */
    position: relative;
    display: inline-block;
  }

  .title-logo-image {
    width: 308px; /* Increased by 10% from 280px */
    max-height: 99px; /* Increased by 10% from 90px */
  }



  .subtitle {
    font-size: 1.8rem;
    display: flex;
    justify-content: center;
    /* Updated subtitle color */
    font-family: 'Abuget', sans-serif;
    /* Custom font for subtitle */
    word-spacing: 2px;
    color: #e95025;
    /* Updated subtitle color */
    text-shadow:
      -0.5px -0.5px 0 rgba(255, 255, 255, 0.8),
      0.5px -0.5px 0 rgba(255, 255, 255, 0.8),
      -0.5px 0.5px 0 rgba(255, 255, 255, 0.8),
      0.5px 0.5px 0 rgba(255, 255, 255, 0.8),
      0 -0.5px 0 rgba(255, 255, 255, 0.6),
      0 0.5px 0 rgba(255, 255, 255, 0.6);
  }

  .dates {
    font-size: 1.1rem;
    display: flex;
    justify-content: flex-end;
    margin-right: 20px;
    margin-bottom: 0;
    /* White color */
    font-family: 'BebasNeue', sans-serif;
    /* Custom font for dates */
    letter-spacing: 1px;
    color: #ffffff;
    text-shadow:
      -1px -1px 0 #ffffff,
      1px -1px 0 #ffffff,
      -1px 1px 0 #ffffff,
      1px 1px 0 #fffcfc,
      -1px 0 0 #ffffff,
      1px 0 0 #ffffff,
      0 -2px 0 #e6e1e1,
      0 2px 0 #e4d6d6;
  }

  .venue {
    font-size: 1rem;
    display: flex;
    justify-content: flex-end;
    margin-right: 20px;
    letter-spacing: 1px;

    margin-top: 0;
    /* White color */
    font-family: 'BebasNeue', sans-serif;
    /* Custom font for venue */
    word-spacing: 2px;
    color: #ffffff;
    text-shadow:
      -1px -1px 0 #ffffff,
      1px -1px 0 #ffffff,
      -1px 1px 0 #ffffff,
      1px 1px 0 #fffcfc,
      -2px 0 0 #ffffff,
      2px 0 0 #ffffff,
      0 -2px 0 #e6e1e1,
      0 2px 0 #e4d6d6;
  }

  /* Mobile Date and Location Styles - Only visible on mobile */
  .homepage-container .mobile-date-location {
    display: flex !important;
    flex-direction: column;
    align-items: center;
    margin-top: 15px;
    gap: 5px;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .mobile-dates {
    font-size: 1.1rem;
    font-family: 'BebasKai', sans-serif;
    letter-spacing: 2px;
    color: #000000;
    text-shadow:
      -1px -1px 0 #ffffff,
      1px -1px 0 #ffffff,
      -1px 1px 0 #ffffff,
      1px 1px 0 #fffcfc,
      -2px 0 0 #ffffff,
      2px 0 0 #ffffff,
      0 -2px 0 #e6e1e1,
      0 2px 0 #e4d6d6;
  }

  .mobile-venue {
    font-size: 1rem;
    font-family: 'BebasKai', sans-serif;
    letter-spacing: 1px;
    color: #000000;
    text-shadow:
      -1px -1px 0 #ffffff,
      1px -1px 0 #ffffff,
      -1px 1px 0 #ffffff,
      1px 1px 0 #fffcfc,
      -2px 0 0 #ffffff,
      2px 0 0 #ffffff,
      0 -2px 0 #e6e1e1,
      0 2px 0 #e4d6d6;
  }

  .mobile-preregister {
    margin-top: 10px;
  }

  .preregister-button {
    display: inline-block;
    padding: 12px 24px;
    background: linear-gradient(135deg, #DAA520, #B8860B); /* Traditional Gold gradient */
    color: #ffffff;
    text-decoration: none;
    border-radius: 8px;
    font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 700;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(218, 165, 32, 0.3);
    border: 2px solid transparent;
  }

  .preregister-button:hover {
    background: linear-gradient(135deg, #B8860B, #9A7209);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(218, 165, 32, 0.4);
    border: 2px solid #DAA520;
    color: #ffffff;
    text-decoration: none;
  }

  .preregister-button:active {
    transform: translateY(0);
    box-shadow: 0 3px 10px rgba(218, 165, 32, 0.3);
  }

  /* Mobile countdown styles - FORCE FIXED at Bottom */
  .countdown-wrapper {
    position: fixed !important;
    bottom: 0px !important;
    left: 0px !important;
    right: 0px !important;
    width: 100vw !important;
    height: auto !important;
    z-index: 99999 !important;
    /* FORCE viewport positioning - NOT page positioning */
    -webkit-transform: translate3d(0, 0, 0) !important;
    transform: translate3d(0, 0, 0) !important;
    will-change: transform !important;
    /* FORCE hardware acceleration */
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    /* PREVENT any parent container interference */
    isolation: isolate !important;
    contain: layout style paint !important;
    /* ENSURE viewport positioning */
    margin: 0 !important;
    padding: 0 !important;
    /* FORCE layer creation */
    -webkit-perspective: 1000px !important;
    perspective: 1000px !important;
    -webkit-transform-style: preserve-3d !important;
    transform-style: preserve-3d !important;
    /* MOBILE SPECIFIC - prevent scrolling with content */
    position: fixed !important;
    top: auto !important;
    bottom: 0px !important;
    /* FORCE it to be relative to viewport, not document */
    -webkit-transform-origin: left bottom !important;
    transform-origin: left bottom !important;
  }

  .countdown-title-wrapper {
    padding: 5px 0;
  }

  .countdown-title-icon {
    width: 24px;
    height: 24px;
  }

  .countdown-title {
    font-size: 1.1rem;
    letter-spacing: 1px;
  }

  .countdown-container {
    padding: 15px 20px 12px 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .countdown-left,
  .countdown-right {
    display: none; /* Hide on mobile - dates and location moved to subtitle area */
  }

  .countdown-center {
    flex: 1;
    width: 100%;
    display: flex;
    justify-content: center;
  }



  .countdown-timer {
    gap: 8px;
  }

  .countdown-numbers-section {
    gap: 8px;
  }

  .countdown-number {
    font-size: 1.8rem;
    color: #ffffff;
    /* White color */
    font-weight: 800;
    line-height: 1;
    margin: 0;
    font-family: 'MinionPro-Bold', serif;
  }

  .countdown-separator {
    font-size: 1.5rem;
    color: #ffffff;
    /* White color */
    font-weight: 800;
    margin: 0 2px;
  }

  .countdown-divider {
    margin: 6px 0;
  }

  .countdown-labels-section {
    gap: 8px;
  }

  .countdown-label-item {
    min-width: 40px;
  }

  .countdown-label {
    font-size: 0.6rem;
    margin: 0;
    color: #ffffff;
    /* White color */
    font-family: 'Baloo2', sans-serif;
  }

  /* Mobile video section */
  .video-section {
    padding: 0 20px;
  }

  .video-title {
    font-size: 1.4rem;
    margin-bottom: 12px;
  }

  /* Mobile new sections */
  .section-heading {
    font-size: 1.6rem;
    margin-bottom: 15px;
    padding: 0 10px;
  }

  .snapshot-section {
    padding: 20px 10px;
    margin-bottom: 20px;
  }

  /* Mobile snapshot cards */
  .snapshot-row-1,
  .snapshot-row-2 {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  /* Center the 5th card (last card in row 2) on mobile */
  .snapshot-row-2 .snapshot-image-card:nth-child(3) {
    grid-column: 1 / -1;
    max-width: 50%;
    margin: 0 auto;
  }

  .snapshot-cards-2025 {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .snapshot-image {
    height: 140px;
    overflow: hidden;
    border-radius: 10px 10px 0 0;
  }

  .snapshot-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    padding: 0 !important;
    margin: 0 !important;
    border: none;
  }

  /* Increase height for first row cards on mobile */
  .snapshot-row-1 .snapshot-image {
    height: 160px;
  }

  .snapshot-text {
    padding: 12px;
    font-size: 0.85rem;
    line-height: 1.2;
  }

  .snapshot-image-card {
    margin: 0;
    padding: 0;
    border-radius: 10px;
    overflow: hidden;
  }

  .snapshot-image-card.new-feature {
    transform: scale(1.01);
  }

  .snapshot-image-card.new-feature:hover {
    transform: scale(1.01) translateY(-2px);
  }

  .vision-main-row {
    flex-direction: column;
    gap: 30px;
  }

  .slideshow-section {
    padding: 30px 15px;
  }

  .slideshow-container {
    margin: 0 auto;
    width: 95%;
    max-width: 600px;
  }

  .slide-image {
    height: 350px;
    object-fit: contain;
    object-position: center;
  }

  .slide-nav {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    padding: 12px 15px;
  }

  .slide-nav.prev {
    left: 15px;
  }

  .slide-nav.next {
    right: 15px;
  }

  .dot {
    width: 12px;
    height: 12px;
  }

  .recognition-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .preview-features {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .testimonials-section {
    padding: 30px 15px;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    max-width: 100%;
  }

  .testimonial-card {
    padding: 25px;
    min-height: auto;
    border-radius: 15px;
  }

  .testimonial-card::before {
    font-size: 3rem;
    top: 10px;
    left: 20px;
  }

  .testimonial-text {
    font-size: 0.95rem;
    line-height: 1.6;
    margin-top: 15px;
    margin-bottom: 20px;
    text-align: left;
  }

  .testimonial-author {
    font-size: 0.85rem;
    line-height: 1.3;
    padding-top: 12px;
  }

  /* Mobile video styles - Single video only */
  .dual-video-container {
    margin-top: 25px;
    gap: 15px;
    padding: 0 15px;
    justify-content: center;
    align-items: center;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .video-item {
    width: 100%;
    padding-bottom: 56.25%;
    /* Standard 16:9 aspect ratio for small mobile */
    border-radius: 10px;
    max-width: 100%;
    position: relative;
    height: 0;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.1);
    display: block;
  }

  /* Ensure second video stays hidden on small mobile */
  .video-item:nth-child(2) {
    display: none;
  }

  .video-item:hover {
    transform: none;
    /* Disable hover effects on mobile */
  }

  .youtube-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 10px;
    border: none;
  }

  /* Mobile Image Layout styles */
  .image-layout-section {
    margin-top: 30px;
    padding: 20px 0;
  }

  .layout-content {
    padding: 0 15px;
  }

  .layout-main-row {
    gap: 20px;
    margin-bottom: 20px;
  }

  .main-layout-img {
    height: 220px;
    border-radius: 10px;
  }

  .layout-text-content {
    padding: 0 5px;
  }

  .layout-heading {
    font-size: 1.6rem;
    margin-bottom: 12px;
  }

  .layout-description {
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .layout-small-images {
    gap: 12px;
    justify-content: center;
    padding: 0 5px;
  }

  .small-image-item {
    min-width: 140px;
    max-width: 160px;
  }

  .small-layout-img {
    height: 130px;
    border-radius: 8px;
  }

  /* Print Media Images Mobile Styles */
  .print-media-images {
    flex-direction: column;
    gap: 10px;
  }

  .print-media-image {
    height: 200px !important;
  }

  /* Full-width Print Media Mobile Styles */
  .print-media-images-fullwidth {
    flex-direction: column;
    gap: 15px;
    padding: 0 12px;
    align-items: center;
    justify-content: center;
  }

  .print-media-image-fullwidth {
    height: 250px !important;
    max-width: 100%;
    object-fit: contain;
    object-position: center;
    padding: 8px !important;
    margin: 0 !important;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .spotlight-subsection-title {
    font-size: 1.4rem;
    margin-bottom: 15px;
    padding: 0 10px;
  }

  /* TV Channels Mobile Styles - 2x3 Card Layout */
  .tv-channels-cards-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, auto);
    gap: 12px;
    padding: 0 12px;
    max-width: 100%;
    justify-items: stretch;
    align-items: stretch;
    margin: 0 auto;
    overflow: hidden;
  }

  .tv-channel-card {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .tv-channel-image-container {
    border-radius: 15px;
    height: 110px;
    padding: 3px;
  }

  /* Government Section Mobile Styles */
  .government-cards-grid {
    padding: 0 12px;
  }

  .government-card {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .government-image-container {
    height: 300px;
    padding: 15px;
  }

  .government-card-info {
    padding: 20px;
  }

  .government-card-title {
    font-size: 1.2rem;
  }

  .government-card-description {
    font-size: 0.9rem;
  }

  .tv-channel-image-fullwidth {
    width: 100% !important;
    height: 200px !important;
    object-fit: contain !important;
    object-position: center !important;
    padding: 5px !important;
    margin: 0 !important;
    border-radius: 8px !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    background: rgba(255, 255, 255, 0.95) !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    display: block !important;
    /* Remove focus/selection styling */
    outline: none !important;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* TV Channels anchor tags mobile styles */
  .tv-channels-grid-fullwidth a {
    display: block;
    width: 100%;
    height: auto;
    max-width: 100%;
    box-sizing: border-box;
    text-decoration: none;
    padding: 3px;
    margin: 0;
    border-radius: 8px;
    transition: transform 0.2s ease;
    /* Remove focus/selection styling */
    outline: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    /* Ensure proper touch handling */
    touch-action: manipulation;
  }

  .tv-channels-grid-fullwidth a:active {
    transform: scale(0.98);
  }

  /* Government Mobile Styles */
  .government-layout-new {
    flex-direction: column;
    gap: 15px;
    padding: 0 10px;
    height: auto;
    max-width: 100%;
    margin: 0 auto;
    justify-content: center;
    align-items: center;
  }

  .government-sidebar-left {
    flex: none;
    width: 100%;
    height: auto;
    order: 1;
    padding: 0 !important;
    margin: 0 !important;
    border-radius: 8px;
    max-width: 100%;
    box-sizing: border-box;
  }

  .government-main-right {
    flex: none;
    width: 100%;
    height: auto;
    order: 2;
    padding: 0 !important;
    margin: 0 !important;
    max-width: 100%;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .government-main-img {
    width: 100%;
    height: 250px;
    object-fit: contain;
    object-position: center;
    padding: 8px !important;
    margin: 0 !important;
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-width: 100%;
    box-sizing: border-box;
    background: rgba(255, 255, 255, 0.95);
  }

  .recognition-info {
    padding: 15px;
  }

  .government-sidebar-image {
    padding: 0 15px 15px 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100px;
    max-width: 100%;
    overflow: hidden;
  }

  .government-sidebar-img {
    width: 100%;
    height: auto;
    max-height: 120px;
    min-height: 80px;
    object-fit: contain;
    object-position: center;
    padding: 0 !important;
    margin: 0 !important;
    border-radius: 6px;
    max-width: 100%;
    box-sizing: border-box;
    display: block;
    background: rgba(255, 255, 255, 0.9);
  }

  .recognition-title {
    font-size: 1.1rem;
    margin-bottom: 8px;
  }

  .ministry-title {
    font-size: 1.3rem;
    margin-bottom: 12px;
  }

  .category-list li {
    font-size: 0.9rem;
    padding: 8px 0;
    line-height: 1.4;
  }

  /* Legacy styles */
  .government-layout {
    flex-direction: column;
    gap: 20px;
    padding: 0 15px;
  }

  .government-main {
    flex: none;
    width: 100%;
  }

  .government-images-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .government-image-grid {
    height: 250px;
  }

  .government-sidebar {
    flex: none;
    width: 100%;
    padding: 20px;
  }

  .ministry-title {
    font-size: 1.2rem;
  }

  .category-list li {
    font-size: 0.85rem;
    padding: 6px 0;
  }
}

/* Extra small mobile devices */
@media (max-width: 360px) {
  /* Extra small mobile countdown wrapper fix */
  .countdown-wrapper {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100vw !important;
    z-index: 10002 !important;
    /* Force viewport positioning for extra small screens */
    -webkit-transform: translate3d(0, 0, 0) !important;
    transform: translate3d(0, 0, 0) !important;
    will-change: transform !important;
    /* Prevent any parent interference */
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    /* Force hardware acceleration */
    isolation: isolate !important;
    contain: layout style paint !important;
    /* Ensure it's positioned relative to viewport */
    margin: 0 !important;
    padding: 0 !important;
    /* Force layer creation for extra small screens */
    -webkit-perspective: 1000px !important;
    perspective: 1000px !important;
    -webkit-transform-style: preserve-3d !important;
    transform-style: preserve-3d !important;
  }

  .video-section {
    margin-top: 20px;
    padding: 0 15px;
  }

  .video-title {
    font-size: 1.2rem;
    margin-bottom: 10px;
  }

  /* Extra small mobile government styles */
  .government-layout-new {
    padding: 0 8px;
    gap: 12px;
  }

  .government-main-img {
    height: 220px;
  }

  .recognition-info {
    padding: 12px;
  }

  .government-sidebar-image {
    padding: 0 12px 12px 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80px;
    max-width: 100%;
    overflow: hidden;
  }

  .government-sidebar-img {
    max-height: 100px;
    min-height: 70px;
    object-fit: contain;
    background: rgba(255, 255, 255, 0.9);
  }

  .recognition-title {
    font-size: 1rem;
  }

  .ministry-title {
    font-size: 1.2rem;
  }

  .category-list li {
    font-size: 0.85rem;
    padding: 6px 0;
  }

  /* Extra small mobile TV channel card styles */
  .tv-channels-cards-grid {
    gap: 8px;
    padding: 0 8px;
  }

  .tv-channel-card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .tv-channel-image-container {
    height: 220px;
    padding: 5px;
  }

  .tv-channel-card-image {
    border-radius: 4px;
  }

  /* Extra small mobile government card styles */
  .government-cards-grid {
    padding: 0 8px;
  }

  .government-card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .government-image-container {
    height: 250px;
    padding: 10px;
  }

  .government-card-info {
    padding: 15px;
  }

  .government-card-title {
    font-size: 1.1rem;
  }

  .government-card-description {
    font-size: 0.85rem;
  }

  /* Extra small mobile print media styles */
  .print-media-images-fullwidth {
    gap: 12px;
    padding: 0 8px;
  }

  .print-media-image-fullwidth {
    height: 220px !important;
    padding: 6px !important;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  }
}

/* Additional mobile responsiveness for government section */
@media (max-width: 480px) {
  /* Force countdown wrapper to stay fixed on all small mobile devices */
  .countdown-wrapper {
    position: fixed !important;
    bottom: 0px !important;
    left: 0px !important;
    right: 0px !important;
    width: 100vw !important;
    z-index: 10004 !important;
    /* Ultra-aggressive fixes for small mobile */
    -webkit-transform: translate3d(0, 0, 0) !important;
    transform: translate3d(0, 0, 0) !important;
    will-change: transform !important;
    /* Prevent any parent interference */
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    /* Force hardware acceleration */
    isolation: isolate !important;
    contain: layout style paint !important;
    /* Ensure it's positioned relative to viewport */
    margin: 0 !important;
    padding: 0 !important;
    /* Force layer creation */
    -webkit-perspective: 1000px !important;
    perspective: 1000px !important;
    -webkit-transform-style: preserve-3d !important;
    transform-style: preserve-3d !important;
    /* Additional mobile-specific fixes */
    -webkit-overflow-scrolling: touch !important;
    /* Force it to stay at bottom */
    top: auto !important;
    bottom: 0px !important;
    /* Prevent any transform inheritance */
    -webkit-transform-origin: center bottom !important;
    transform-origin: center bottom !important;
  }

  .government-section {
    width: 100%;
    margin-bottom: 30px;
    padding: 0;
  }

  .government-layout-new {
    max-width: 100%;
    overflow: hidden;
  }

  .government-sidebar-left,
  .government-main-right {
    width: 100% !important;
    max-width: 100% !important;
    overflow: hidden;
  }

  .government-main-img,
  .government-sidebar-img {
    width: 100% !important;
    max-width: 100% !important;
    display: block;
  }
}

/* ULTIMATE MOBILE FIX - Override EVERYTHING for countdown wrapper */
@media (max-width: 768px) {
  /* NUCLEAR OPTION - Override any potential parent container interference */
  html .countdown-wrapper,
  body .countdown-wrapper,
  .App .countdown-wrapper,
  .homepage-container .countdown-wrapper,
  div .countdown-wrapper,
  * .countdown-wrapper,
  .countdown-wrapper {
    position: fixed !important;
    bottom: 0px !important;
    left: 0px !important;
    right: 0px !important;
    top: auto !important;
    width: 100vw !important;
    height: auto !important;
    z-index: 999999 !important;
    /* FORCE viewport positioning - NOT document positioning */
    -webkit-transform: translate3d(0, 0, 0) !important;
    transform: translate3d(0, 0, 0) !important;
    will-change: transform !important;
    /* FORCE hardware acceleration */
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    /* PREVENT any parent interference */
    isolation: isolate !important;
    contain: layout style paint !important;
    /* ENSURE viewport positioning */
    margin: 0 !important;
    padding: 0 !important;
    /* FORCE layer creation */
    -webkit-perspective: 1000px !important;
    perspective: 1000px !important;
    -webkit-transform-style: preserve-3d !important;
    transform-style: preserve-3d !important;
    /* MOBILE SPECIFIC - prevent scrolling with content */
    -webkit-overflow-scrolling: touch !important;
    /* FORCE it to be relative to viewport, not document */
    -webkit-transform-origin: left bottom !important;
    transform-origin: left bottom !important;
    /* OVERRIDE any potential parent transforms */
    -webkit-transform-box: view-box !important;
    transform-box: view-box !important;
  }

  /* ENSURE parent containers don't interfere */
  .homepage-container {
    position: relative !important;
    transform: none !important;
    -webkit-transform: none !important;
  }

  .App {
    position: relative !important;
    transform: none !important;
    -webkit-transform: none !important;
  }
}

/* 24-inch Monitor Responsive Design (1920px and above) */
@media (min-width: 1920px) {
  /* Homepage container optimizations for 24-inch monitors */
  .homepage-container {
    padding: 100px 0 250px;
    max-width: 100%;
  }

  /* Main title styling for 24-inch monitors */
  .main-title {
    font-size: 8rem;
    letter-spacing: 2px;
    margin-bottom: 40px;
  }

  .title-logo-image {
    width: 660px; /* Increased by 10% from 600px */
    max-height: 198px; /* Increased by 10% from 180px */
  }

  .festival-tagline {
    font-size: 2.2rem;
    margin: 30px auto;
    max-width: 1200px; /* Further increased for 24-inch monitors */
    line-height: 1.3;
    letter-spacing: 1px;
    white-space: nowrap; /* Ensure single line display on large screens */
    padding: 18px 40px; /* Increased horizontal padding for better text accommodation */
  }



  /* Subtitle for 24-inch monitors */
  .subtitle {
    font-size: 4rem;
    font-weight: 550;
    margin-top: 50px;
    line-height: 1.3;
    max-width: 100%;
    font-family: 'Abuget', sans-serif;
    word-spacing: 8px;
    color: #e95025;
    text-shadow:
      -2px -2px 0 #ffffff,
      2px -2px 0 #ffffff,
      -2px 2px 0 #ffffff,
      2px 2px 0 #fffcfc,
      -3px 0 0 #ffffff,
      3px 0 0 #ffffff,
      0 -3px 0 #e6e1e1,
      0 3px 0 #e4d6d6;
  }

  /* Countdown timer for 24-inch monitors */
  .countdown-title {
    font-size: 2.2rem;
    letter-spacing: 3px;
  }

  .countdown-container {
    padding: 35px 60px 30px 60px;
  }

  .countdown-dates {
    font-size: 1.6rem;
    letter-spacing: 3px;
  }

  .location-text {
    font-size: 1.4rem;
    letter-spacing: 3px;
  }

  .countdown-number {
    font-size: 3rem;
    font-weight: 800;
    font-family: 'MinionPro-Bold', serif;
  }

  .countdown-label {
    font-size: 1rem;
    letter-spacing: 2px;
  }

  /* Section headings for 24-inch monitors */
  .section-heading {
    font-size: 3.5rem;
    margin-bottom: 60px;
    letter-spacing: 2px;
  }

  /* Snapshot cards optimization for 24-inch monitors */
  .snapshot-cards-2024,
  .snapshot-cards-2025 {
    max-width: 1800px;
    gap: 40px;
  }

  .snapshot-row-1 {
    gap: 40px;
    margin-bottom: 40px;
  }

  .snapshot-row-2 {
    gap: 40px;
  }

  .snapshot-image {
    height: 250px;
  }

  .snapshot-row-1 .snapshot-image {
    height: 350px;
  }

  .snapshot-text {
    padding: 35px;
    font-size: 1.3rem;
  }

  /* Video section for 24-inch monitors */
  .video-section {
    max-width: 1600px;
    padding: 0 60px;
  }

  .video-title {
    font-size: 2.5rem;
    margin-bottom: 30px;
  }

  .dual-video-container {
    gap: 50px;
    padding: 0 60px;
  }

  /* Vision section for 24-inch monitors */
  .vision-content {
    max-width: 1800px;
    padding: 0 60px;
  }

  .vision-main-row {
    gap: 80px;
  }

  /* Slideshow for 24-inch monitors */
  .slideshow-container {
    max-width: 1600px;
  }

  .slide-image {
    height: 600px;
  }

  /* Testimonials for 24-inch monitors */
  .testimonials-grid {
    max-width: 1800px;
    gap: 40px;
  }

  .testimonial-card {
    padding: 40px;
    min-height: 300px;
  }

  .testimonial-text {
    font-size: 1.2rem;
    line-height: 1.8;
  }

  /* Government and TV sections for 24-inch monitors */
  .government-layout-new,
  .tv-channels-grid-fullwidth {
    max-width: 1800px;
    gap: 40px;
  }

  /* Print media images for 24-inch monitors */
  .print-media-images-fullwidth {
    gap: 30px;
    padding: 0 40px;
  }

  .print-media-image-fullwidth {
    height: 400px;
  }
}