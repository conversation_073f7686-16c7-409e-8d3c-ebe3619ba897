import { useState, useEffect } from 'react';

/**
 * Custom hook for responsive image loading based on device type
 * @param {string} imageName - The base name of the image (e.g., 'cycling.jpg')
 * @param {string} category - The image category (e.g., 'gallery', 'team', 'icons', etc.)
 * @param {string} fallbackPath - Optional fallback path for the original image
 * @returns {string} - The appropriate image path for the current device
 */
export const useResponsiveImage = (imageName, category, fallbackPath = null) => {
  const [imagePath, setImagePath] = useState('');
  const [deviceType, setDeviceType] = useState('laptop');

  // Determine device type based on screen width
  const getDeviceType = () => {
    const width = window.innerWidth;
    if (width <= 768) return 'mobile';
    if (width >= 1920) return 'monitor';
    return 'laptop';
  };

  // Update device type on window resize
  useEffect(() => {
    const handleResize = () => {
      setDeviceType(getDeviceType());
    };

    // Set initial device type
    setDeviceType(getDeviceType());

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Update image path when device type or image name changes
  useEffect(() => {
    const getImagePath = () => {
      try {
        // Try to load the device-specific image
        const deviceSpecificPath = require(`../assets/${deviceType}/${category}/${imageName}`);
        return deviceSpecificPath;
      } catch (error) {
        console.warn(`[useResponsiveImage] Device-specific image not found: ${deviceType}/${category}/${imageName}`);
        
        // Fallback to original path if provided
        if (fallbackPath) {
          try {
            const fallbackImage = require(`../assets/${fallbackPath}`);
            return fallbackImage;
          } catch (fallbackError) {
            console.error(`[useResponsiveImage] Fallback image not found: ${fallbackPath}`);
          }
        }
        
        // Return empty string if all fails
        return '';
      }
    };

    setImagePath(getImagePath());
  }, [imageName, category, deviceType, fallbackPath]);

  return imagePath;
};

/**
 * Get device-specific image path without React hook (for use in non-component contexts)
 * @param {string} imageName - The base name of the image
 * @param {string} category - The image category
 * @param {string} fallbackPath - Optional fallback path
 * @returns {string} - The appropriate image path
 */
export const getResponsiveImagePath = (imageName, category, fallbackPath = null) => {
  const getDeviceType = () => {
    const width = window.innerWidth;
    if (width <= 768) return 'mobile';
    if (width >= 1920) return 'monitor';
    return 'laptop';
  };

  const deviceType = getDeviceType();

  try {
    // Try to load the device-specific image
    const deviceSpecificPath = require(`../assets/${deviceType}/${category}/${imageName}`);
    return deviceSpecificPath;
  } catch (error) {
    console.warn(`[getResponsiveImagePath] Device-specific image not found: ${deviceType}/${category}/${imageName}`);
    
    // Fallback to original path if provided
    if (fallbackPath) {
      try {
        const fallbackImage = require(`../assets/${fallbackPath}`);
        return fallbackImage;
      } catch (fallbackError) {
        console.error(`[getResponsiveImagePath] Fallback image not found: ${fallbackPath}`);
      }
    }
    
    return '';
  }
};

/**
 * Image category mappings for easier usage
 */
export const IMAGE_CATEGORIES = {
  GALLERY: 'gallery',
  TEAM: 'team',
  ICONS: 'icons',
  SOCIAL_ICONS: 'socialIcons',
  LOGOS: 'logos',
  SPONSORS: 'sponsors',
  SCHOOLS: 'schools',
  POSTERS: 'posters',
  SNAPSHOTS: 'snapshots',
  SPOTLIGHTS: 'spotlights',
  BACKGROUNDS: 'backgrounds'
};

/**
 * Helper function to create responsive image component props
 * @param {string} imageName - The base name of the image
 * @param {string} category - The image category
 * @param {string} alt - Alt text for the image
 * @param {string} fallbackPath - Optional fallback path
 * @returns {object} - Props object for img element
 */
export const createResponsiveImageProps = (imageName, category, alt, fallbackPath = null) => {
  const imagePath = getResponsiveImagePath(imageName, category, fallbackPath);
  
  return {
    src: imagePath,
    alt: alt,
    loading: 'lazy', // Enable lazy loading for performance
    onError: (e) => {
      console.error(`[ResponsiveImage] Failed to load image: ${imagePath}`);
      // You could set a default placeholder image here
      // e.target.src = '/path/to/placeholder.jpg';
    }
  };
};

export default useResponsiveImage;
