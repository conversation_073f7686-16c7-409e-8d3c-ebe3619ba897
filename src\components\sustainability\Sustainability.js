import React from 'react';
import './Sustainability.css';

const Sustainability = ({ language = 'english' }) => {

  const content = {
    english: {
      heading: "Sustainability",
      description: "The Sempozhil 2024 Sustainability Report reveals how India's biggest urban village festival blended tradition with climate action. Held in Chennai, the event celebrated rural heritage while committing to eco-conscious practices. By following global GHG standards, Sempozhil isn't just preserving culture—it's pioneering greener festivals for the future.",
      downloadButton: "Download Full Report",
      tableData: {
        title: "Sustainability Metrics",
        headers: ["Sr. No.", "Emission Source", "Description", "Methodology", "Emissions (KgCO2e)"],
        scope1: {
          title: "Scope 1",
          rows: [
            ["1", "Diesel Generator (DG)", "Emissions from diesel combustion for electricity", "Emissions calculated from stationary combustion are estimated using diesel consumption data.", "31,463"]
          ]
        },
        scope3_commuting: {
          title: "Volunteers & Visitors Commuting (C9)",
          rows: [
            ["1", "Commuting to Event Venue", "Emissions from volunteers and visitors commuting to the event venue.", "Estimated based on the commuting data, mode of transport, and emission factors.", "4,206"]
          ]
        },
        scope3_transport: {
          title: "Animal Transport (C4)",
          rows: [
            ["1", "Transport to Event Venue", "Emissions from the transport of animals to and from the event venue.", "Estimated based on the number of trips, distance traveled, and type of fuel used.", "1,882"]
          ]
        },
        scope3_materials: {
          title: "Materials Used During Event (C1)",
          rows: [
            ["1", "Event Materials Production", "Emissions from the production and use of materials like mats, sheets, and decorative papers.", "Estimated using emission factors of the materials based on life-cycle assessment (LCA) data from the literature research papers.", "53,720"]
          ]
        },
        scope3_food: {
          title: "Food Consumption (C1)",
          rows: [
            ["1", "Food Production & Transport", "Emissions from the production and transportation of food consumed by human & animals during the event.", "Calculated using emission factors for specific food items based on their life-cycle assessment (LCA) data from the literature research papers.", "4,145"]
          ]
        },
        emissionsAvoided: {
          title: "Emissions Avoided",
          rows: [
            ["1", "Waste Diversion Program", "Diverting waste from landfills to recycling facilities, reducing methane emissions.", "Estimated based on the quantity of waste diverted and corresponding emission savings per kg.", "-1132"]
          ]
        }
      }
    },
    tamil: {
      heading: "நிலைத்தன்மை",
      description: "செம்பொழில் 2024 நிலைத்தன்மை அறிக்கை இந்தியாவின் மிகப்பெரிய நகர்ப்புற கிராம திருவிழா எவ்வாறு பாரம்பரியத்தை காலநிலை நடவடிக்கையுடன் இணைத்தது என்பதை வெளிப்படுத்துகிறது. சென்னையில் நடைபெற்ற இந்த நிகழ்வு கிராமப்புற பாரம்பரியத்தை கொண்டாடியது மற்றும் சுற்றுச்சூழல் உணர்வுள்ள நடைமுறைகளுக்கு உறுதியளித்தது. உலகளாவிய GHG தரநிலைகளைப் பின்பற்றுவதன் மூலம், செம்பொழில் கலாச்சாரத்தை பாதுகாப்பது மட்டுமல்லாமல், எதிர்காலத்திற்கான பசுமையான திருவிழாக்களுக்கு முன்னோடியாக உள்ளது.",
      downloadButton: "முழு அறிக்கையை பதிவிறக்கவும்",
      tableData: {
        title: "நிலைத்தன்மை அளவீடுகள்",
        headers: ["வ.எண்.", "உமிழ்வு மூலம்", "விளக்கம்", "முறையியல்", "உமிழ்வுகள் (KgCO2e)"],
        scope1: {
          title: "நோக்கம் 1",
          rows: [
            ["1", "டீசல் ஜெனரேட்டர் (DG)", "மின்சாரத்திற்கான டீசல் எரிப்பிலிருந்து உமிழ்வுகள்", "நிலையான எரிப்பிலிருந்து உமிழ்வுகள் டீசல் நுகர்வு தரவைப் பயன்படுத்தி மதிப்பிடப்படுகின்றன.", "31,463"]
          ]
        },
        scope3_commuting: {
          title: "தன்னார்வலர்கள் மற்றும் பார்வையாளர்கள் பயணம் (C9)",
          rows: [
            ["1", "நிகழ்வு இடத்திற்கு பயணம்", "நிகழ்வு இடத்திற்கு தன்னார்வலர்கள் மற்றும் பார்வையாளர்கள் பயணம் செய்வதால் ஏற்படும் உமிழ்வுகள்.", "பயண தரவு, போக்குவரத்து முறை மற்றும் உமிழ்வு காரணிகளின் அடிப்படையில் மதிப்பிடப்பட்டது.", "4,206"]
          ]
        },
        scope3_transport: {
          title: "விலங்கு போக்குவரத்து (C4)",
          rows: [
            ["1", "நிகழ்வு இடத்திற்கு போக்குவரத்து", "நிகழ்வு இடத்திற்கு மற்றும் அங்கிருந்து விலங்குகளின் போக்குவரத்தால் ஏற்படும் உமிழ்வுகள்.", "பயணங்களின் எண்ணிக்கை, பயணித்த தூரம் மற்றும் பயன்படுத்தப்பட்ட எரிபொருளின் வகையின் அடிப்படையில் மதிப்பிடப்பட்டது.", "1,882"]
          ]
        },
        scope3_materials: {
          title: "நிகழ்வின் போது பயன்படுத்தப்பட்ட பொருட்கள் (C1)",
          rows: [
            ["1", "நிகழ்வு பொருட்கள் உற்பத்தி", "பாய்கள், தாள்கள் மற்றும் அலங்கார காகிதங்கள் போன்ற பொருட்களின் உற்பத்தி மற்றும் பயன்பாட்டால் ஏற்படும் உமிழ்வுகள்.", "இலக்கிய ஆராய்ச்சி ஆவணங்களிலிருந்து வாழ்க்கை சுழற்சி மதிப்பீடு (LCA) தரவின் அடிப்படையில் பொருட்களின் உமிழ்வு காரணிகளைப் பயன்படுத்தி மதிப்பிடப்பட்டது.", "53,720"]
          ]
        },
        scope3_food: {
          title: "உணவு நுகர்வு (C1)",
          rows: [
            ["1", "உணவு உற்பத்தி மற்றும் போக்குவரத்து", "நிகழ்வின் போது மனிதர்கள் மற்றும் விலங்குகளால் நுகரப்படும் உணவின் உற்பத்தி மற்றும் போக்குவரத்தால் ஏற்படும் உமிழ்வுகள்.", "இலக்கிய ஆராய்ச்சி ஆவணங்களிலிருந்து அவற்றின் வாழ்க்கை சுழற்சி மதிப்பீடு (LCA) தரவின் அடிப்படையில் குறிப்பிட்ட உணவு பொருட்களுக்கான உமிழ்வு காரணிகளைப் பயன்படுத்தி கணக்கிடப்பட்டது.", "4,145"]
          ]
        },
        emissionsAvoided: {
          title: "தவிர்க்கப்பட்ட உமிழ்வுகள்",
          rows: [
            ["1", "கழிவு திசைதிருப்பல் திட்டம்", "கழிவுகளை நிலப்பரப்பிலிருந்து மறுசுழற்சி வசதிகளுக்கு திருப்பி விடுதல், மீத்தேன் உமிழ்வுகளை குறைத்தல்.", "திருப்பி விடப்பட்ட கழிவுகளின் அளவு மற்றும் ஒரு கிலோவிற்கு தொடர்புடைய உமிழ்வு சேமிப்பின் அடிப்படையில் மதிப்பிடப்பட்டது.", "-1132"]
          ]
        }
      }
    }
  };

  const handleDownload = () => {
    // Convert Google Drive view link to direct download link
    const driveLink = "https://drive.google.com/file/d/1MKmtkpFZQaOZXnIJeZdVIIvKa4ZOYfbc/view?usp=drive_link";
    const fileId = driveLink.match(/\/d\/([a-zA-Z0-9-_]+)/)[1];
    const downloadLink = `https://drive.google.com/uc?export=download&id=${fileId}`;

    // Create a temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadLink;
    link.download = 'Sempozhil_2024_Sustainability_Report.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const currentContent = content[language];

  // Card-based layout renderer for all screen sizes
  const renderCardTable = () => {
    const renderCardSection = (title, rows, isEmissionsAvoided = false) => (
      <div className={`card-scope-section ${isEmissionsAvoided ? 'emissions-avoided-card' : ''}`} key={title}>
        <h3 className="card-scope-title">
          {title}
          {isEmissionsAvoided && <span className="importance-indicator">★</span>}
        </h3>
        <div className="card-rows-container">
          {rows.map((row, index) => (
            <div key={index} className="card-table-row">
              <div className="card-row-header">{row[1]}</div>
              <div className="card-content-grid">
                <div className="card-field">
                  <div className="card-field-value">{row[2]}</div>
                </div>
                <div className="card-field">
                  <div className="card-field-label">Methodology</div>
                  <div className="card-field-value">{row[3]}</div>
                </div>
                <div className="card-field card-emissions-field">
                  <div className="card-field-label">Emissions</div>
                  <div className={`card-field-value card-emissions-value ${isEmissionsAvoided ? 'card-emissions-avoided' : ''}`}>
                    {row[4]} KgCO2e
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );

    return (
      <div className="card-table-container">
        {renderCardSection(currentContent.tableData.scope1.title, currentContent.tableData.scope1.rows)}
        {renderCardSection(currentContent.tableData.scope3_commuting.title, currentContent.tableData.scope3_commuting.rows)}
        {renderCardSection(currentContent.tableData.scope3_transport.title, currentContent.tableData.scope3_transport.rows)}
        {renderCardSection(currentContent.tableData.scope3_materials.title, currentContent.tableData.scope3_materials.rows)}
        {renderCardSection(currentContent.tableData.scope3_food.title, currentContent.tableData.scope3_food.rows)}
        {renderCardSection(currentContent.tableData.emissionsAvoided.title, currentContent.tableData.emissionsAvoided.rows, true)}
      </div>
    );
  };

  return (
    <div className="sustainability-container">
      {/* Page Heading */}
      <h1 className="sustainability-heading">{currentContent.heading}</h1>

      {/* Description Content */}
      <div className="sustainability-description">
        <p>{currentContent.description}</p>
      </div>

      {/* Download Button and Table Card */}
      <div className="table-section">
        <div className="table-header">
          <h2>{currentContent.tableData.title}</h2>
          <button className="download-btn" onClick={handleDownload}>
            {currentContent.downloadButton}
          </button>
        </div>

        {/* Card-based Layout for All Screen Sizes */}
        {renderCardTable()}
      </div>
    </div>
  );
};

export default Sustainability;