# Apache configuration for Sempozhil React App
# This file will be copied to the build directory

# Enable URL rewriting
RewriteEngine On

# Handle client-side routing for React Router
# Redirect all requests to index.html except for files that exist
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]

# Security headers
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff

    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"

    # Prevent clickjacking
    Header always set X-Frame-Options SAMEORIGIN

    # Referrer policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # iOS Safari specific headers for audio files
    <FilesMatch "\.(mp3|m4a|ogg|wav|webm)$">
        Header set Access-Control-Allow-Origin "*"
        Header set Access-Control-Allow-Methods "GET, OPTIONS"
        Header set Access-Control-Allow-Headers "Content-Type, Range"
        Header set Accept-Ranges bytes
        Header set Cache-Control "public, max-age=********"
    </FilesMatch>
</IfModule>

# Cache static assets for better performance
<IfModule mod_expires.c>
    ExpiresActive on
    
    # Cache CSS and JavaScript for 1 year
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType application/x-javascript "access plus 1 year"
    
    # Cache images for 1 year
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"

    # Cache audio files for 1 year
    ExpiresByType audio/mpeg "access plus 1 year"
    ExpiresByType audio/mp3 "access plus 1 year"
    

    
    # Cache fonts for 1 year
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # Cache HTML for 1 hour (to allow for updates)
    ExpiresByType text/html "access plus 1 hour"
    
    # Cache manifest and other files for 1 week
    ExpiresByType application/manifest+json "access plus 1 week"
    ExpiresByType text/cache-manifest "access plus 1 week"
</IfModule>

# Enable compression for better performance
<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
</IfModule>

# Set proper MIME types
<IfModule mod_mime.c>
    # Audio - iOS Safari compatible MIME types
    AddType audio/mpeg .mp3
    AddType audio/mp4 .m4a
    AddType audio/ogg .ogg
    AddType audio/ogg .oga
    AddType audio/webm .webma
    AddType audio/wav .wav

    # Additional iOS Safari audio support
    AddType audio/x-mpeg .mp3
    AddType audio/mp3 .mp3

    # Video
    AddType video/mp4 mp4 m4v
    AddType video/ogg ogv
    AddType video/webm webm
    AddType video/webm webmv
    
    # Fonts
    AddType application/font-woff woff
    AddType application/font-woff2 woff2
    AddType application/vnd.ms-fontobject eot
    AddType font/opentype otf
    AddType font/truetype ttf
    
    # Other
    AddType application/manifest+json webmanifest
    AddType text/cache-manifest appcache
</IfModule>

# Prevent access to sensitive files
<FilesMatch "\.(env|log|htaccess|htpasswd|ini|phps|fla|psd|log|sh)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>
