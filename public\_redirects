# Redirect rules for React Router
# This ensures that all routes are handled by React Router

# Handle React Router routes
/*    /index.html   200

# Ensure static assets are served correctly
/static/*  /static/:splat  200
/assets/*  /assets/:splat  200

# Cache static assets
/static/*  Cache-Control: public,max-age=31536000,immutable
/assets/*  Cache-Control: public,max-age=31536000,immutable

# Audio files
*.mp3  Content-Type: audio/mpeg
*.mp3  Access-Control-Allow-Origin: *

# Image files
*.jpg  Content-Type: image/jpeg
*.jpeg Content-Type: image/jpeg
*.png  Content-Type: image/png
*.jpg  Access-Control-Allow-Origin: *
*.jpeg Access-Control-Allow-Origin: *
*.png  Access-Control-Allow-Origin: *
