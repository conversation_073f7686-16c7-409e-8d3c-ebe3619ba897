/* Global Audio Player Styles */
.global-audio-player {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 9999;
  /* Ensure complete isolation from parent styles */
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
  /* Prevent inheritance of parent transforms */
  isolation: isolate;
  /* Force hardware acceleration */
  will-change: transform;
  -webkit-transform: translateY(-50%) translateZ(0);
  transform: translateY(-50%) translateZ(0);
}

/* Simple Circular Play Button */
.audio-play-button {
  width: 60px;
  height: 60px;
  border: none;
  border-radius: 50%;
  background: rgba(139, 69, 19, 0.9); /* Brown color matching the image */
  color: white;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  /* box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3); */
  border: 3px solid rgba(255, 255, 255, 0.8);
}

/* Icon styling */
.audio-play-button svg {
  transition: all 0.3s ease;
}

/* Loading icon animation */
.audio-play-button .loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.audio-play-button:hover {
  background: rgba(139, 69, 19, 1);
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.audio-play-button:active {
  transform: scale(0.95);
}

.audio-play-button:disabled {
  background: rgba(139, 69, 19, 0.5);
  cursor: not-allowed;
  transform: none;
}

.audio-play-button.playing {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    /* box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3); */
  }
  50% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5), 0 0 20px rgba(139, 69, 19, 0.4);
  }
  100% {
    /* box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3); */
  }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .global-audio-player {
    position: fixed !important;
    right: 15px;
    top: 50% !important;
    z-index: 9999 !important; /* Higher z-index to ensure it's above everything */
    /* iOS Safari specific fixes */
    -webkit-transform: translateY(-50%) translateZ(0);
    transform: translateY(-50%) translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    /* Ensure it stays fixed during scroll */
    will-change: transform;
    -webkit-overflow-scrolling: touch;
    /* Force hardware acceleration and prevent parent transform interference */
    isolation: isolate;
    contain: layout style paint;
    /* Ensure it's positioned relative to viewport, not parent */
    margin: 0;
    padding: 0;
    /* Prevent any parent container from affecting positioning */
    left: auto;
    bottom: auto;
  }

  .audio-play-button {
    width: 50px;
    height: 50px;
    font-size: 18px;
    /* iOS touch optimization */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    /* Ensure button stays clickable */
    pointer-events: auto;
    /* Additional positioning fixes */
    position: relative;
    z-index: 1;
  }

  .audio-play-button svg {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 480px) {
  .global-audio-player {
    position: fixed !important;
    right: 10px;
    top: 50% !important;
    z-index: 10000 !important; /* Even higher z-index for small screens */
    /* Additional iOS fixes for small screens */
    -webkit-transform: translateY(-50%) translateZ(0);
    transform: translateY(-50%) translateZ(0);
    /* Enhanced mobile positioning */
    will-change: transform;
    -webkit-overflow-scrolling: touch;
    /* Prevent scrolling interference */
    touch-action: none;
    /* Force hardware acceleration and prevent parent transform interference */
    isolation: isolate;
    contain: layout style paint;
    /* Ensure it's positioned relative to viewport, not parent */
    margin: 0;
    padding: 0;
    /* Prevent any parent container from affecting positioning */
    left: auto;
    bottom: auto;
    /* Additional mobile fixes */
    -webkit-perspective: 1000px;
    perspective: 1000px;
  }

  .audio-play-button {
    width: 45px;
    height: 45px;
    font-size: 16px;
    /* Enhanced iOS touch handling */
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    touch-action: manipulation;
    /* Ensure visibility and interaction */
    pointer-events: auto;
    position: relative;
    z-index: 1;
    /* Additional mobile button fixes */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  .audio-play-button svg {
    width: 16px;
    height: 16px;
  }
}

/* iOS-specific fixes */
@supports (-webkit-touch-callout: none) {
  .global-audio-player {
    /* Force hardware acceleration on iOS */
    position: fixed !important;
    top: 50% !important;
    z-index: 10001 !important; /* Highest z-index for iOS */
    -webkit-transform: translateY(-50%) translateZ(0);
    transform: translateY(-50%) translateZ(0);
    will-change: transform;
    /* iOS Safari specific positioning fixes */
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    /* Prevent iOS scroll interference */
    -webkit-overflow-scrolling: touch;
    /* Force hardware acceleration and prevent parent transform interference */
    isolation: isolate;
    contain: layout style paint;
    /* Ensure it's positioned relative to viewport, not parent */
    margin: 0;
    padding: 0;
    /* Prevent any parent container from affecting positioning */
    left: auto;
    bottom: auto;
    /* Additional iOS-specific fixes */
    -webkit-perspective: 1000px;
    perspective: 1000px;
    /* Force layer creation */
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }

  .audio-play-button {
    /* Improve button responsiveness on iOS */
    -webkit-tap-highlight-color: transparent;
    -webkit-appearance: none;
    appearance: none;
    /* Ensure iOS touch events work */
    pointer-events: auto;
    touch-action: manipulation;
    /* Additional iOS button fixes */
    position: relative;
    z-index: 1;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
}

/* Accessibility */
.audio-play-button:focus {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .audio-play-button {
    transition: none;
  }

  .audio-play-button.playing {
    animation: none;
  }

  .audio-play-button:hover {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .audio-play-button {
    background: black;
    color: white;
    border: 3px solid white;
  }
}
