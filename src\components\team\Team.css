/* Team Page Main Styles with Tamil Cultural Design */
.team-container {
  min-height: 100vh;
  width: 100vw;
  /* Remove custom background - inherit from App.css global background */
  background: none;
  position: relative;
  padding: 126px 1rem 150px 1rem; /* Top padding for navbar (71px + margin), increased bottom padding for footer clearance */
  color: #333333; /* Tamil cultural body text color */
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  margin-bottom: 0; /* Remove margin-bottom to prevent overlap */
}

/* iOS Safari fix - not needed since we inherit background from App.css */

/* 24-inch Monitor - inherit background from App.css */
@media (min-width: 1920px) {
  .team-container {
    padding-bottom: 200px !important; /* Increased bottom padding instead of margin */
    margin-bottom: 0 !important; /* Remove margin to prevent overlap */
  }
}

/* Mobile - inherit background from App.css */
@media (max-width: 768px) {
  .team-container {
    width: 100vw !important;
    min-height: 100vh !important;
    padding-bottom: 180px !important; /* Increased bottom padding for mobile */
    margin-bottom: 0 !important; /* Remove margin to prevent overlap */
  }
}

.team-header {
  text-align: center;
  margin-bottom: 3rem;
  animation: fadeInUp 1.2s ease-out;
}

.team-heading {
  font-size: 2.4rem; /* Matched to other pages like Schools and Sponsors */
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  color: #4B0000; /* Deep Maroon */
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: 2px; /* Matched to other pages */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
  margin: 0;
  line-height: 1.2;
}

.team-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem; /* Reduced gap */
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Animation */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .team-heading {
    font-size: 1.8rem; /* Matched to mobile sizes in other pages */
    text-shadow:
      -1px -1px 0 #ffffff,
      1px -1px 0 #ffffff,
      -1px 1px 0 #ffffff,
      1px 1px 0 #fffcfc,
      -2px 0 0 #ffffff,
      2px 0 0 #ffffff,
      0 -2px 0 #e6e1e1,
      0 2px 0 #e4d6d6;
  }
}

@media screen and (min-width: 768px) {
  .team-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.8rem; /* Reduced gap */
  }

  .team-heading {
    font-size: 2.4rem; /* Matched to tablet sizes in other pages */
  }
}

@media screen and (min-width: 992px) {
  .team-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem; /* Reduced gap for better laptop fit */
  }

  .team-container {
    padding: 100px 2rem 180px 2rem; /* Increased bottom padding */
  }
}

@media screen and (min-width: 1200px) {
  .team-heading {
    font-size: 2.4rem; /* Consistent with desktop size */
  }

  .team-container {
    padding: 160px 2rem 200px 2rem; /* Increased bottom padding */
  }

  .team-grid {
    gap: 2.5rem; /* Slightly larger gap for large screens */
  }
}

/* Specific optimization for 24-inch monitors and laptops */
@media screen and (min-width: 1366px) and (max-width: 1920px) {
  .team-grid {
    gap: 2rem;
    max-width: 1100px; /* Slightly smaller max width for better fit */
  }
}
