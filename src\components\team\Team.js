import React, { useState } from 'react';
import './Team.css';
import { teamMembers } from './teamData';
import TeamMemberCard from './TeamMemberCard';
import TeamMemberDetail from './TeamMemberDetail';

const Team = ({ language = 'english', onPopupToggle }) => {
  const [selectedMember, setSelectedMember] = useState(null);
  const [showDetail, setShowDetail] = useState(false);

  const content = {
    english: {
      heading: "Team Behind Sempozhil",
      knowMore: "KNOW MORE"
    },
    tamil: {
      heading: "செம்பொழில் குழு",
      knowMore: "மேலும் அறிய"
    }
  };

  const currentContent = content[language];

  const handleKnowMore = (member) => {
    setSelectedMember(member);
    setShowDetail(true);
    // Notify parent component that popup is open
    if (onPopupToggle) {
      onPopupToggle(true);
    }
  };

  const handleCloseDetail = () => {
    setShowDetail(false);
    setSelectedMember(null);
    // Notify parent component that popup is closed
    if (onPopupToggle) {
      onPopupToggle(false);
    }
  };

  return (
    <div className="team-container">
      {/* Main Team Grid */}
      {!showDetail && (
        <>
          <div className="team-header">
            <h1 className="team-heading">{currentContent.heading}</h1>
          </div>
          
          <div className="team-grid">
            {teamMembers.map((member) => (
              <TeamMemberCard
                key={member.id}
                member={member}
                onKnowMore={handleKnowMore}
                language={language}
              />
            ))}
          </div>
        </>
      )}

      {/* Team Member Detail View */}
      {showDetail && selectedMember && (
        <TeamMemberDetail
          member={selectedMember}
          onClose={handleCloseDetail}
          language={language}
        />
      )}
    </div>
  );
};

export default Team;
