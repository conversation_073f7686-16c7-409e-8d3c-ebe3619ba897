import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import logoImage from "../../assets/logo.png";
import "../../css/Navbar.css";
const Navbar = ({ onLanguageChange }) => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [language, setLanguage] = useState('EN');
  // eslint-disable-next-line no-unused-vars
  const location = useLocation();

  // Disable body scroll when menu is open
  useEffect(() => {
    document.body.style.overflow = menuOpen ? "hidden" : "auto";
  }, [menuOpen]);

  // Toggle menu open state
  const toggleMenu = () => {
    setMenuOpen(prev => !prev);
  };

  // Close menu when clicking a link or overlay
  const closeMenu = () => {
    setMenuOpen(false);

    // Force scroll to top when navigating
    setTimeout(() => {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'instant'
      });
      // Additional fallbacks
      document.documentElement.scrollTop = 0;
      document.body.scrollTop = 0;
    }, 10);
  };

  // Toggle language
  // eslint-disable-next-line no-unused-vars
  const toggleLanguage = () => {
    const newLanguage = language === 'EN' ? 'TA' : 'EN';
    setLanguage(newLanguage);
    // Pass the language change to parent component
    if (onLanguageChange) {
      onLanguageChange(newLanguage === 'EN' ? 'english' : 'tamil');
    }
  };

  return (
    <>
      <nav className="navbar">
        <div className="navbar__left">
          <Link to="/" className="navbar__logo">
            <img src={logoImage} alt="Sempozhil Logo" className="navbar__logo-img" />
          </Link>
        </div>

        <div className="navbar__right">
          {/* <button
            className="language-toggle-btn"
            onClick={toggleLanguage}
            aria-label="Toggle language"
          >
            {language}
          </button> */}
          <button
            className={`navbar__toggle ${menuOpen ? "open" : ""}`}
            onClick={toggleMenu}
            aria-label="Toggle menu"
          >
            <span className="bar"></span>
            <span className="bar"></span>
            <span className="bar"></span>
          </button>
        </div>
      </nav>

      {/* Overlay */}
      {menuOpen && <div className="navbar__overlay" onClick={closeMenu}></div>}

      {/* Drawer menu */}
      <div className={`navbar__menu ${menuOpen ? "open" : ""}`}>
        <Link to="/" onClick={closeMenu}>Home</Link>
        <Link to="/schools" onClick={closeMenu}>For Schools</Link>
        <Link to="/stall-booking" onClick={closeMenu}>For Businesses</Link>
        <Link to="/sponsors" onClick={closeMenu}>For Sponsors / Partners</Link>
        <Link to="/team" onClick={closeMenu}>The Team</Link>
        <Link to="/sustainability" onClick={closeMenu}>Sustainability Report</Link>
      </div>
    </>
  );
};

export default Navbar;
