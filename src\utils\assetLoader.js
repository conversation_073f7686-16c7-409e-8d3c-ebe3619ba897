// Asset loader utility for AWS Amplify compatibility
// This utility helps load assets without relying on environment variables

export const testAssetAccess = async (assetPath) => {
  try {
    const response = await fetch(assetPath, { method: 'HEAD' });
    return {
      success: response.ok,
      status: response.status,
      path: assetPath
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      path: assetPath
    };
  }
};

export const findWorkingAssetPath = async (assetPaths) => {
  for (const path of assetPaths) {
    const result = await testAssetAccess(path);
    if (result.success) {
      console.log(`[AssetLoader] Found working path: ${path}`);
      return path;
    } else {
      console.log(`[AssetLoader] Failed path: ${path} - ${result.error || result.status}`);
    }
  }
  
  console.error('[AssetLoader] No working asset paths found');
  return null;
};

export const getAssetPaths = (filename, baseUrl = window.location.origin) => {
  return [
    // Direct bundled paths (most reliable for AWS Amplify)
    `/static/media/${filename}`,
    `${baseUrl}/static/media/${filename}`,
    
    // Public folder paths
    `/assets/audio/${filename}`,
    `${baseUrl}/assets/audio/${filename}`,
    `./assets/audio/${filename}`,
    `assets/audio/${filename}`,
    
    // Root paths
    `/${filename}`,
    `${baseUrl}/${filename}`,
    `./${filename}`,
    filename
  ];
};
