import React from 'react';
import './TeamMemberCard.css';
import { useResponsiveImage } from '../../hooks/useResponsiveImage';

const TeamMemberCard = ({ member, onKnowMore, language }) => {
  // Responsive social media icons
  const facebookIcon = useResponsiveImage('black_facebook.png', 'social-media', 'social-media/black_facebook.png');
  const linkedinIcon = useResponsiveImage('black-linkedIn.png', 'social-media', 'social-media/black-linkedIn.png');
  const instagramIcon = useResponsiveImage('black-instagram.png', 'social-media', 'social-media/black-instagram.png');
  const twitterIcon = useResponsiveImage('balck-X.png', 'social-media', 'social-media/balck-X.png');

  const content = {
    english: {
      knowMore: "KNOW MORE"
    },
    tamil: {
      knowMore: "மேலும் அறிய"
    }
  };

  const currentContent = content[language];

  const getSocialIcon = (platform) => {
    switch (platform) {
      case 'facebook':
        return facebookIcon;
      case 'linkedin':
        return linkedinIcon;
      case 'instagram':
        return instagramIcon;
      case 'twitter':
        return twitterIcon;
      default:
        return null;
    }
  };

  return (
    <div className="team-member-card">
      <div className="card">
        <div className="img-container">
          <img src={member.image} alt={member.name} />
        </div>
        
        <h3 className="member-name">{member.name}</h3>
        
        <div className="member-info">
          <p className="member-role">{member.role}</p>
          <p className="member-location">{member.location}</p>
        </div>

        <div className="member-description">
          {member.description ? (
            member.description.split('\n').map((line, index) => (
              <p key={index} className="description-line">{line}</p>
            ))
          ) : (
            <div className="description-placeholder"></div>
          )}
        </div>

        <div className="know-more-section">
          <button 
            className="know-more-btn"
            onClick={() => onKnowMore(member)}
          >
            {currentContent.knowMore}
          </button>
        </div>

        <div className="social-media">
          {Object.entries(member.socialMedia).map(([platform, url]) => {
            const icon = getSocialIcon(platform);
            if (!icon || !url) return null;
            
            return (
              <div key={platform} className="social-link">
                <a 
                  href={url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  aria-label={`${member.name} on ${platform}`}
                >
                  <img src={icon} alt={platform} />
                </a>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default TeamMemberCard;
