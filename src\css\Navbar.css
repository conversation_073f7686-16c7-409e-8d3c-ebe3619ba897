/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: sans-serif;
}

/* Navbar container fixed at top */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 65px;
  background: #ffff00;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  z-index: 1100; /* Higher than menu */
}

/* Golden border under the navbar */
.navbar::after {
  content: '';
  position: fixed;
  top: 65px;
  left: 0;
  right: 0;
  height: 6px;
  background: #f3a813;
  z-index: 1101;
}

.navbar__left {
  display: flex;
  align-items: center;
  flex: 1;
}

.navbar__right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.language-toggle-btn {
  background: linear-gradient(135deg, #DAA520, #B8860B); /* Traditional Gold gradient */
  color: white;
  border: none;
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 600;
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 35px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.5px;
}

.language-toggle-btn:hover {
  background: linear-gradient(135deg, #B8860B, #9A7209);
  transform: scale(1.05);
}

.navbar__logo {
  display: flex;
  align-items: center;
  height: 100%;
}

.navbar__logo-img {
  height: 75px;
  width: auto;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.navbar__logo-img:hover {
  transform: scale(1.05);
}

/* Toggle button */
.navbar__toggle {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 5px;
  height: 30px;
  width: 30px;
  padding: 0;
  z-index: 1200; /* Highest to be above overlay and menu */
  transition: transform 0.3s ease;
}

.navbar__toggle .bar {
  width: 25px;
  height: 3px;
  background: #333;
  transition: all 0.3s ease;
  border-radius: 2px;
  transform-origin: center;
}

/* Hamburger -> X animation */
.navbar__toggle.open .bar:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.navbar__toggle.open .bar:nth-child(2) {
  opacity: 0;
}

.navbar__toggle.open .bar:nth-child(3) {
  transform: rotate(-45deg) translate(5px, -5px);
}

/* Overlay that covers entire screen */
.navbar__overlay {
  position: fixed;
  inset: 0; /* shorthand for top:0; right:0; bottom:0; left:0; */
  background: rgba(58, 54, 54, 0.5);
  z-index: 100; /* Between toggle button (1200) and menu (1100) */
}

/* Side menu panel */
.navbar__menu {
  position: fixed;
  top: 71px; /* Just below navbar (65px + 6px golden border) */
  right: -300px; /* Hidden offscreen initially */
  width: 300px;
  height: calc(100vh - 71px); /* Full height minus navbar and golden border */
  background: #fff;
  padding: 20px 20px 0 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  transition: right 0.3s ease;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  z-index: 1100; /* Under overlay and toggle */
}

/* Show menu by moving it in */
.navbar__menu.open {
  right: 0;
}

.navbar__menu a {
  font-size: 18px;
  color: #333333; /* Tamil cultural body text */
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 500;
  text-decoration: none;
  user-select: none;
  transition: color 0.2s ease;
  text-align: left;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  letter-spacing: 0.3px;
}

.navbar__menu a:hover {
  color: #DAA520; /* Traditional Gold on hover */
}
