import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { logWarning } from '../../utils/errorSuppression';
import './GlobalAudioPlayer.css';

// Try to import audio file directly for better AWS Amplify compatibility
let importedAudioSrc = null;
try {
  // This will work if the audio file is moved to src/assets
  importedAudioSrc = require('../../assets/audio/bg.mp3');
} catch (error) {
  // Fallback if import fails
  logWarning('[GlobalAudioPlayer] Could not import audio file directly, using path-based approach', 'GlobalAudioPlayer');
}

const GlobalAudioPlayer = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const audioRef = useRef(null);

  // Audio file path - AWS Amplify compatible path resolution
  // Multiple fallback paths for better compatibility
  const getAudioSources = () => {
    const sources = [];
    const baseUrl = window.location.origin;
    // Don't rely on process.env.PUBLIC_URL as it may not be available in AWS Amplify
    const publicUrl = (typeof process !== 'undefined' && process.env && process.env.PUBLIC_URL) ? process.env.PUBLIC_URL : '';

    // Try imported audio first (best for AWS Amplify bundling)
    if (importedAudioSrc) {
      sources.push(importedAudioSrc);
      logWarning(`[GlobalAudioPlayer] Using imported audio source: ${importedAudioSrc}`, 'GlobalAudioPlayer');
    }

    // AWS Amplify and production paths - comprehensive fallback strategy
    // These paths work for both production and development
    sources.push(`${baseUrl}/assets/audio/bg.mp3`);
    sources.push('/assets/audio/bg.mp3');
    sources.push('./assets/audio/bg.mp3');
    sources.push('assets/audio/bg.mp3');

    // AWS Amplify specific paths for bundled assets
    sources.push('/static/media/bg.mp3');
    sources.push(`${baseUrl}/static/media/bg.mp3`);

    // If we have a public URL, try it
    if (publicUrl) {
      sources.push(`${baseUrl}${publicUrl}/assets/audio/bg.mp3`);
      sources.push(`${publicUrl}/assets/audio/bg.mp3`);
    }

    // Try to find bundled audio file with hash (this will be the most reliable for AWS Amplify)
    sources.push(`${baseUrl}/static/media/bg.4163aafb188da85191ae.mp3`);
    sources.push('/static/media/bg.4163aafb188da85191ae.mp3');

    return sources;
  };

  const [currentSourceIndex, setCurrentSourceIndex] = useState(0);
  const audioSources = getAudioSources();
  const audioSrc = audioSources[currentSourceIndex];

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    // Test if audio file is accessible
    const testAudioAccess = async () => {
      try {
        const response = await fetch(audioSrc, { method: 'HEAD' });
        logWarning(`[GlobalAudioPlayer] Audio file accessibility test: ${response.status} ${response.statusText}`, 'GlobalAudioPlayer');
        logWarning(`[GlobalAudioPlayer] Testing audio source: ${audioSrc}`, 'GlobalAudioPlayer');
        logWarning(`[GlobalAudioPlayer] Current environment: ${(typeof process !== 'undefined' && process.env && process.env.NODE_ENV) || 'unknown'}`, 'GlobalAudioPlayer');
        logWarning(`[GlobalAudioPlayer] Window origin: ${window.location.origin}`, 'GlobalAudioPlayer');
        if (!response.ok) {
          logWarning(`[GlobalAudioPlayer] Audio file not accessible at: ${audioSrc}`, 'GlobalAudioPlayer');
        } else {
          logWarning(`[GlobalAudioPlayer] Audio file accessible successfully`, 'GlobalAudioPlayer');
        }
      } catch (error) {
        logWarning(`[GlobalAudioPlayer] Audio file accessibility test failed: ${error.message}`, 'GlobalAudioPlayer');
      }
    };

    testAudioAccess();

    const handleLoadedData = () => {
      setIsLoaded(true);
      logWarning('[GlobalAudioPlayer] Audio loaded successfully', 'GlobalAudioPlayer');
    };

    const handleError = (error) => {
      const errorMsg = error.target ? error.target.error : error;
      logWarning(`[GlobalAudioPlayer] Audio loading error: ${errorMsg?.message || errorMsg || 'Unknown error'}`, 'GlobalAudioPlayer');
      logWarning(`[GlobalAudioPlayer] Audio src: ${audioSrc}`, 'GlobalAudioPlayer');
      logWarning(`[GlobalAudioPlayer] User agent: ${navigator.userAgent}`, 'GlobalAudioPlayer');
      logWarning(`[GlobalAudioPlayer] Error code: ${error.target?.error?.code || 'N/A'}`, 'GlobalAudioPlayer');
      logWarning(`[GlobalAudioPlayer] Network state: ${audio?.networkState || 'N/A'}`, 'GlobalAudioPlayer');
      logWarning(`[GlobalAudioPlayer] Ready state: ${audio?.readyState || 'N/A'}`, 'GlobalAudioPlayer');
      logWarning(`[GlobalAudioPlayer] Environment: ${(typeof process !== 'undefined' && process.env && process.env.NODE_ENV) || 'unknown'}`, 'GlobalAudioPlayer');
      logWarning(`[GlobalAudioPlayer] Base URL: ${window.location.origin}`, 'GlobalAudioPlayer');

      // Try next audio source if available
      if (currentSourceIndex < audioSources.length - 1) {
        const nextIndex = currentSourceIndex + 1;
        const nextSrc = audioSources[nextIndex];
        logWarning(`[GlobalAudioPlayer] Trying fallback source ${nextIndex + 1}/${audioSources.length}: ${nextSrc}`, 'GlobalAudioPlayer');
        setCurrentSourceIndex(nextIndex);
        return; // Don't set error state yet, give fallback a chance
      }

      logWarning(`[GlobalAudioPlayer] All audio sources failed. Hiding audio player.`, 'GlobalAudioPlayer');
      setHasError(true);
      setIsLoaded(false);
    };

    const handleCanPlay = () => {
      setIsLoaded(true);
      logWarning('[GlobalAudioPlayer] Audio can play', 'GlobalAudioPlayer');
    };

    // iOS-specific audio loading handler
    const handleLoadStart = () => {
      logWarning('[GlobalAudioPlayer] Audio loading started', 'GlobalAudioPlayer');
    };

    const handleProgress = () => {
      logWarning('[GlobalAudioPlayer] Audio loading progress', 'GlobalAudioPlayer');
    };

    // Add event listeners
    audio.addEventListener('loadeddata', handleLoadedData);
    audio.addEventListener('error', handleError);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('progress', handleProgress);

    // Set volume to 70%
    audio.volume = 0.7;

    // iOS Safari requires explicit loading
    if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
      logWarning('[GlobalAudioPlayer] iOS detected, calling load()', 'GlobalAudioPlayer');
      audio.load();
    }

    // Log the audio source for debugging
    logWarning(`[GlobalAudioPlayer] Audio source: ${audioSrc}`, 'GlobalAudioPlayer');
    logWarning(`[GlobalAudioPlayer] Environment: ${(typeof process !== 'undefined' && process.env && process.env.NODE_ENV) || 'unknown'}`, 'GlobalAudioPlayer');
    logWarning(`[GlobalAudioPlayer] PUBLIC_URL: ${(typeof process !== 'undefined' && process.env && process.env.PUBLIC_URL) || 'undefined'}`, 'GlobalAudioPlayer');
    logWarning(`[GlobalAudioPlayer] Window location: ${window.location.origin}`, 'GlobalAudioPlayer');

    // Cleanup
    return () => {
      audio.removeEventListener('loadeddata', handleLoadedData);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('progress', handleProgress);
    };
  }, [audioSrc, currentSourceIndex, audioSources]);

  // Handle source changes when fallback is triggered
  useEffect(() => {
    const audio = audioRef.current;
    if (audio && audio.src !== audioSrc) {
      logWarning(`[GlobalAudioPlayer] Switching to audio source: ${audioSrc}`, 'GlobalAudioPlayer');
      audio.src = audioSrc;
      audio.load();
    }
  }, [audioSrc]);

  const togglePlayPause = async () => {
    const audio = audioRef.current;
    if (!audio || hasError) return;

    try {
      if (isPlaying) {
        await audio.pause();
        setIsPlaying(false);
        logWarning('[GlobalAudioPlayer] Audio paused', 'GlobalAudioPlayer');
      } else {
        // iOS Safari specific handling
        if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
          // Ensure audio is loaded before playing on iOS
          if (audio.readyState < 2) {
            audio.load();
            await new Promise((resolve) => {
              audio.addEventListener('canplay', resolve, { once: true });
            });
          }
        }

        // Modern browsers require user interaction before playing audio
        const playPromise = audio.play();
        if (playPromise !== undefined) {
          await playPromise;
          setIsPlaying(true);
          logWarning('[GlobalAudioPlayer] Audio playing', 'GlobalAudioPlayer');
        }
      }
    } catch (error) {
      logWarning(`[GlobalAudioPlayer] Play/pause error: ${error.message}`, 'GlobalAudioPlayer');
      // Don't set error state for common iOS audio restrictions
      if (!error.message.includes('NotAllowedError') && !error.message.includes('AbortError')) {
        setHasError(true);
      }
    }
  };

  // Don't render if there's an error
  if (hasError) {
    return null;
  }

  // Use portal to render outside of any transform contexts
  const audioPlayerElement = (
    <div className="global-audio-player">
      <audio
        ref={audioRef}
        src={audioSrc}
        preload="metadata"
        loop
        playsInline
        webkit-playsinline="true"
        crossOrigin="anonymous"
        controls={false}
        muted={false}
      />

      <button
        className={`audio-play-button ${isPlaying ? 'playing' : 'paused'}`}
        onClick={togglePlayPause}
        disabled={!isLoaded}
        aria-label={isPlaying ? 'Pause background music' : 'Play background music'}
        title={isPlaying ? 'Pause background music' : 'Play background music'}
      >
        {isLoaded ? (
          isPlaying ? (
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
            </svg>
          ) : (
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M8 5v14l11-7z"/>
            </svg>
          )
        ) : (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" className="loading-icon">
            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" fill="none" strokeDasharray="31.416" strokeDashoffset="31.416">
              <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416;0 31.416" repeatCount="indefinite"/>
              <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416;-31.416" repeatCount="indefinite"/>
            </circle>
          </svg>
        )}
      </button>
    </div>
  );

  // Render using portal to document.body to avoid any parent transform contexts
  return createPortal(audioPlayerElement, document.body);
};

export default GlobalAudioPlayer;
