/* Custom Font Declarations for Schools Page */
@font-face {
  font-family: 'AbrilFatface';
  src: url('../../assets/fonts/AbrilFatface-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Abuget';
  src: url('../../assets/fonts/Abuget.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}



@font-face {
  font-family: 'Baloo2';
  src: url('../../assets/fonts/Baloo2-VariableFont_wght.ttf') format('truetype');
  font-weight: 100 900; /* Support full weight range for variable font */
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MinionPro';
  src: url('../../assets/fonts/MinionPro-Bold_0.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* Schools Page Styles with Tamil Cultural Typography */
.schools-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 126px 20px 20px 20px; /* Added bottom padding for fixed countdown container, adjusted for new navbar height */
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.7;
  margin-bottom: 20px;
  color: #333333; /* Tamil cultural body text */
}

/* Header Section */
.schools-header {
  text-align: center;
  margin-bottom: 40px;
}



.schools-heading {
  font-size: 2.4rem;
  font-weight: 700;
  color: #4B0000; /* Deep Maroon */
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  text-align: center;
  margin-bottom: 40px;
  line-height: 1.2;
  text-transform: uppercase;
  letter-spacing: 2px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
}

.schools-subtitle {
  width: 60%;
  font-size: 1.4rem;
  color: #000000; /* Simple black color */
  font-family: 'Baloo2', sans-serif;
  line-height: 1.6;
  margin: 20px auto 25px auto; /* Custom margin - separate from parent */
  letter-spacing: 0.3px;
  background: rgba(255, 255, 255, 0.7); /* Semi-transparent white background */
  padding: 15px 20px;
  border-radius: 10px;
  text-shadow:
    -1px -1px 0 #ffffff,
    1px -1px 0 #ffffff,
    -1px 1px 0 #ffffff,
    1px 1px 0 #fffcfc,
    -2px 0 0 #ffffff,
    2px 0 0 #ffffff,
    0 -2px 0 #e6e1e1,
    0 2px 0 #e4d6d6;
  text-align: center; /* Center the text content */
}

/* Introduction Section */
.introduction-section {
  text-align: center;
  margin-bottom: 50px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.introduction-text {
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 20px;
  color: #000000;
  font-weight: 500;
}

.mission-text {
  font-size: 1.5rem;
  line-height: 1.6;
  font-weight: 500;
  margin-bottom: 30px;
  font-family: 'Akhand', sans-serif;
  font-style: normal;
  color: #e95128; /* Updated color without stroke */
  font-weight: 600;
  background: rgba(255, 255, 255, 0.7); /* Semi-transparent white background */
  padding: 15px 20px;
  border-radius: 10px;
  text-align: center;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 30px;
  text-shadow:
    -1px -1px 0 #ffffff,
    1px -1px 0 #ffffff,
    -1px 1px 0 #ffffff,
    1px 1px 0 #fffcfc,
    -2px 0 0 #ffffff,
    2px 0 0 #ffffff,
    0 -2px 0 #e6e1e1,
    0 2px 0 #e4d6d6;
}

.event-title {
  font-size: 2rem;
  font-weight: 700;
  color: #4B0000;
  text-align: center;
  margin-bottom: 30px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Activities Section */
.activities-section {
  margin-bottom: 60px;
}

.activities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.activity-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15px;
  padding: 30px;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.activity-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

.activity-image {
  width: 100%;
  height: 200px;
  margin-bottom: 20px;
  border-radius: 10px;
  overflow: hidden;
}

.activity-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.activity-img:hover {
  transform: scale(1.05);
}

.activity-title {
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: #4B0000;
}

.activity-description {
  font-size: 1rem;
  line-height: 1.5;
  color: #2c3e50;
  font-weight: 500;
}

/* Video Section */
.video-section {
  margin-bottom: 60px;
  text-align: center;
}

/* Posters Section */
.posters-section {
  margin-bottom: 60px;
  text-align: center;
}

.posters-container {
  display: flex;
  gap: 30px;
  justify-content: center;
  flex-wrap: wrap;
  max-width: 1000px;
  margin: 0 auto;
}

.poster-item {
  flex: 1;
  min-width: 300px;
  max-width: 450px;
}

.poster-image {
  width: 100%;
  height: auto;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.poster-image:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

/* Pricing Section */
.pricing-section {
  margin-bottom: 60px;
  text-align: center;
}

.pricing-title {
  font-size: 2rem;
  font-weight: 700;
  color: #4B0000;
  text-align: center;
  margin-bottom: 30px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.pricing-options {
  display: flex;
  gap: 30px;
  justify-content: center;
  flex-wrap: wrap;
  max-width: 800px;
  margin: 0 auto;
}

.pricing-option {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  padding: 25px;
  border: 2px solid #4B0000;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  min-width: 300px;
  flex: 1;
}

.pricing-option:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.price {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #4B0000;
  margin-bottom: 10px;
}

.package-details {
  display: block;
  font-size: 1rem;
  color: #2c3e50;
  line-height: 1.4;
}

.video-title {
  font-size: 2rem;
  font-weight: 700;
  color: #4B0000;
  text-align: center;
  margin-bottom: 30px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.video-container {
  max-width: 800px;
  margin: 0 auto;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.video-container iframe {
  border-radius: 15px;
}

/* Form Section */
.form-section {
  text-align: center;
}

.form-title {
  font-size: 2rem;
  font-weight: 700;
  color: #4B0000;
  text-align: center;
  margin-bottom: 30px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.form-container {
  max-width: 1000px;
  margin: 0 auto;
  border-radius: 0;
  overflow: visible;
  box-shadow: none;
  background: transparent;
  border: none;
  padding: 0;
}

.form-container iframe {
  height: 1200px;
  min-height: 1000px;
  max-height: none;
  overflow: hidden;
  background: transparent !important;
  border: none !important;
  display: block;
  width: 100%;
}

/* Desktop: Hide scrollbars but allow content to be fully visible */
.form-iframe {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.form-iframe::-webkit-scrollbar {
  display: none; /* WebKit browsers */
}

/* Additional styling to ensure scrollable Google Form integration */
.form-container {
  position: relative;
}

/* Desktop: seamless embedding without scrollbars */
.form-container iframe {
  background-color: transparent !important;
  opacity: 1;
  -webkit-overflow-scrolling: touch;
}

/* Form direct access button styling */
.form-direct-access {
  background: rgba(255, 255, 255, 0.7);
  padding: 10px;
  margin-top: 50px;
  border-radius: 10px;
  text-align: center;
}

.form-direct-access p {
  color: #333333;
  font-size: 1rem;
  margin-bottom: 10px;
}

.form-direct-link {
  display: inline-block;
  padding: 12px 24px;
  background-color: #e67e22;
  color: white;
  text-decoration: none;
  border-radius: 5px;
  font-weight: bold;
  margin-top: 10px;
  transition: background-color 0.3s ease;
}

.form-direct-link:hover {
  background-color: #d35400;
  color: white;
  text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .schools-container {
    padding: 100px 15px 50px 15px; /* Added bottom padding for fixed countdown container */
  }

  .schools-heading {
    font-size: 2.2rem;
    color: #4B0000; /* Deep Maroon */
    font-family: 'Noto Serif Tamil', 'Merriweather', serif;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
    text-align: center;
  }

  .schools-subtitle {
    font-size: 1.2rem;
    color: #000000; /* Simple black color */
    font-family: 'Baloo2', sans-serif;
    font-weight: 400;
    padding: 12px 15px;
    text-shadow:
      -1px -1px 0 #ffffff,
      1px -1px 0 #ffffff,
      -1px 1px 0 #ffffff,
      1px 1px 0 #fffcfc,
      -2px 0 0 #ffffff,
      2px 0 0 #ffffff,
      0 -2px 0 #e6e1e1,
      0 2px 0 #e4d6d6;
  }

  /* Add white stroke to event-title and mission-text for mobile only */
  .event-title {
    text-shadow:
      1px 1px 1px rgba(255, 255, 255, 0.6),
      -1px -1px 1px rgba(255, 255, 255, 0.6),
      1px -1px 1px rgba(255, 255, 255, 0.6),
      -1px 1px 1px rgba(255, 255, 255, 0.6),
      0 0 1px rgba(255, 255, 255, 0.1); /* White glow effect for readability */
  }

  .mission-text {
    padding: 12px 15px;
    text-shadow:
      -1px -1px 0 #ffffff,
      1px -1px 0 #ffffff,
      -1px 1px 0 #ffffff,
      1px 1px 0 #fffcfc,
      -2px 0 0 #ffffff,
      2px 0 0 #ffffff,
      0 -2px 0 #e6e1e1,
      0 2px 0 #e4d6d6;
  }



  .event-title,
  .video-title,
  .form-title {
    font-size: 1.6rem;
  }

  .activities-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .activity-card {
    padding: 20px;
  }

  .activity-title {
    font-size: 1.2rem;
  }

  .posters-container {
    flex-direction: column;
    gap: 20px;
  }

  .poster-item {
    min-width: 100%;
  }

  .video-title,
  .form-title {
    font-size: 1.5rem;
  }

  .video-container iframe {
    height: 250px;
  }

  .form-container {
    border-radius: 8px;
    overflow: visible;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background: white;
    border: 1px solid #e0e0e0;
    padding: 10px;
    margin: 10px 0;
    max-height: 600px;
  }

  .form-container iframe {
    height: 500px;
    min-height: 400px;
    max-height: 600px;
    overflow: auto;
    background: white !important;
    border: none !important;
  }

  /* Mobile: Enable scrollbars and make them visible */
  .form-iframe {
    scrollbar-width: thin !important; /* Firefox - enable scrollbar */
    -ms-overflow-style: auto !important; /* Internet Explorer 10+ - enable scrollbar */
  }

  .form-iframe::-webkit-scrollbar {
    display: block !important; /* WebKit browsers - show scrollbar */
    width: 8px;
  }

  .form-iframe::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  .form-iframe::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
  }

  .form-iframe::-webkit-scrollbar-thumb:hover {
    background: #999;
  }
}

@media (max-width: 480px) {
  .schools-heading {
    font-size: 1.6rem;
    color: #4B0000; /* Deep Maroon */
    font-family: 'Noto Serif Tamil', 'Merriweather', serif;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
    text-align: center;
  }

  .schools-subtitle {
    font-size: 1rem;
    color: #000000; /* Simple black color */
    font-family: 'Baloo2', sans-serif;
    font-weight: 300;
    padding: 10px 12px;
    width: 90%;
    text-shadow:
      -1px -1px 0 #ffffff,
      1px -1px 0 #ffffff,
      -1px 1px 0 #ffffff,
      1px 1px 0 #fffcfc,
      -2px 0 0 #ffffff,
      2px 0 0 #ffffff,
      0 -2px 0 #e6e1e1,
      0 2px 0 #e4d6d6;
  }



  .introduction-text,
  .mission-text {
    font-size: 1.2rem;
    color: #e95128; /* Updated color without stroke */
    font-family: 'Akhand', sans-serif;
    font-weight: 600;
    padding: 10px 12px;
    width: 90%;
    margin-left: auto;
    margin-right: auto;
    text-shadow:
      -1px -1px 0 #ffffff,
      1px -1px 0 #ffffff,
      -1px 1px 0 #ffffff,
      1px 1px 0 #fffcfc,
      -2px 0 0 #ffffff,
      2px 0 0 #ffffff,
      0 -2px 0 #e6e1e1,
      0 2px 0 #e4d6d6;
  }

  .event-title {
    text-shadow:
      1px 1px 1px rgba(255, 255, 255, 0.6),
      -1px -1px 1px rgba(255, 255, 255, 0.6),
      1px -1px 1px rgba(255, 255, 255, 0.6),
      -1px 1px 1px rgba(255, 255, 255, 0.6),
      0 0 1px rgba(255, 255, 255, 0.1); /* White glow effect for readability */
  }

  .event-title,
  .video-title,
  .form-title {
    font-size: 1.4rem;
  }

  .activity-image {
    height: 150px;
  }

  .posters-container {
    gap: 15px;
  }

  .poster-image {
    border-radius: 10px;
  }

  .video-container iframe {
    height: 200px;
  }

  .form-container {
    border-radius: 8px;
    overflow: visible;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background: white;
    border: 1px solid #e0e0e0;
    padding: 8px;
    margin: 10px 0;
    max-height: 500px;
  }

  .form-container iframe {
    height: 450px;
    min-height: 350px;
    max-height: 500px;
    overflow: auto;
    background: white !important;
    border: none !important;
  }

  /* Small Mobile: Enable scrollbars and make them visible */
  .form-iframe {
    scrollbar-width: thin !important; /* Firefox - enable scrollbar */
    -ms-overflow-style: auto !important; /* Internet Explorer 10+ - enable scrollbar */
  }

  .form-iframe::-webkit-scrollbar {
    display: block !important; /* WebKit browsers - show scrollbar */
    width: 6px;
  }

  .form-iframe::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .form-iframe::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
  }

  .form-iframe::-webkit-scrollbar-thumb:hover {
    background: #999;
  }
}
