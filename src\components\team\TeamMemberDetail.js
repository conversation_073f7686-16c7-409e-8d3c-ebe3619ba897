import React, { useEffect } from 'react';
import './TeamMemberDetail.css';
import closeIcon from '../../assets/close.png';

const TeamMemberDetail = ({ member, onClose, language }) => {
  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [onClose]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, []);

  if (!member) return null;

  const handleOverlayClick = (event) => {
    if (event.target === event.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="team-detail-overlay" onClick={handleOverlayClick}>
      <div className="team-detail-container">
        <div className="close-button">
          <button onClick={onClose} aria-label="Close">
            <img src={closeIcon} alt="Close" />
          </button>
        </div>

        <div className="detail-content">
          <div className="detail-image">
            <img src={member.image} alt={member.name} />
          </div>

          <h1 className="detail-name">{member.name}</h1>
          
          <h2 className="detail-role">
            {member.role}
            {member.company && (
              <>
                <br />
                {member.company}
              </>
            )}
          </h2>

          {member.websites && member.websites.length > 0 && (
            <div className="website-links">
              {member.websites.map((website, index) => (
                <a
                  key={index}
                  href={website.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="website-logo-link"
                >
                  <img src={website.logo} alt={website.name} />
                </a>
              ))}
            </div>
          )}

          <div className="bio-content">
            {member.bio.map((paragraph, index) => (
              <p key={index}>{paragraph}</p>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeamMemberDetail;
