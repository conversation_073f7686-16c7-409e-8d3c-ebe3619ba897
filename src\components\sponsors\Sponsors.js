import React from 'react';
import './Sponsors.css';
import OptimizedYouTubeEmbed from '../common/OptimizedYouTubeEmbed';

const Sponsors = ({ language = 'english' }) => {
  const content = {
    english: {
      heading: "INVITING SPONSORS / PARTNERS",
      titleText: "Sempozhil is more than just an event—it's an ecosystem dedicated to health, sustainability, and fair trade, creating a transparent, conscious community where people know exactly where their food comes from and who is behind it.",
      aboutHeading: "About this Event",
      detailedDescription: "India's largest Urban Village Festival, Sempozhil is a one-of-a-kind celebration of traditional farming, native livestock, food, culture, arts, and sports. Held in Chennai, the event honors rural heritage while committing to eco-conscious practices. By adhering to global GHG standards, Sempozhil is not just preserving culture—it's pioneering the future of greener festivals.",
      organizationInfo: "Organized by the Thondaimandalam Foundation, a non-profit established in 2018 with the mission to enhance and sustain rural and peri-urban livelihoods. Our focus areas include natural farming, water conservation, native biodiversity preservation, education access and quality, and the promotion of traditional arts and culture.",
      invitationText: "We invite institutions, organizations, and businesses to join us in advancing this cause and taking the message of sustainability, health, and fair trade to greater heights.",
      contactText: "Contact us to know more about how you can be part of this",
      contactNumber: "+91 98409 04244"
    },
    tamil: {
      heading: "ஸ்பான்சர்கள் / கூட்டாளர்களுக்கு",
      titleText: "செம்பொழில் வெறும் ஒரு நிகழ்வு அல்ல—இது சுகாதாரம், நிலைத்தன்மை மற்றும் நியாயமான வர்த்தகத்திற்கு அர்ப்பணிக்கப்பட்ட ஒரு சுற்றுச்சூழல் அமைப்பாகும், இது மக்கள் தங்கள் உணவு எங்கிருந்து வருகிறது மற்றும் அதன் பின்னால் யார் இருக்கிறார்கள் என்பதை சரியாக அறிந்த ஒரு வெளிப்படையான, விழிப்புணர்வுள்ள சமூகத்தை உருவாக்குகிறது.",
      aboutHeading: "செம்பொழில் பற்றி",
      detailedDescription: "இந்தியாவின் மிகப்பெரிய நகர்ப்புற கிராம திருவிழா, செம்பொழில் பாரம்பரிய விவசாயம், பூர்வீக கால்நடைகள், உணவு, கலாச்சாரம், கலைகள் மற்றும் விளையாட்டுகளின் தனித்துவமான கொண்டாட்டமாகும்.",
      organizationInfo: "2018 இல் நிறுவப்பட்ட தொண்டைமண்டலம் அறக்கட்டளையால் ஏற்பாடு செய்யப்பட்டது, கிராமப்புற மற்றும் நகர்ப்புற வாழ்வாதாரங்களை மேம்படுத்துவது மற்றும் நிலைநிறுத்துவது என்ற நோக்கத்துடன்.",
      invitationText: "இந்த நோக்கத்தை முன்னெடுப்பதில் எங்களுடன் சேர நிறுவனங்கள், அமைப்புகள் மற்றும் வணிகங்களை அழைக்கிறோம்.",
      contactText: "இதில் எப்படி பங்கேற்கலாம் என்பதை அறிய எங்களை தொடர்பு கொள்ளுங்கள்",
      contactNumber: "+91 98409 04244"
    }
  };

  const currentContent = content[language];

  return (
    <div className="sponsors-container">
      {/* Header Section */}
      <div className="sponsors-header">
        <h1 className="sponsors-heading">{currentContent.heading}</h1>
        <p className="sponsors-title-text">{currentContent.titleText}</p>
      </div>

      {/* Video Section */}
      <div className="sponsors-video-section">
        <OptimizedYouTubeEmbed
          videoId="reYOzMm09UQ"
          title="Sempozhil Sponsors Video"
          autoplay={false}
          muted={true}
          enableLazyLoad={true}
          showPlayButton={true}
          className="sponsors-video"
          height="400px"
        />
      </div>

      {/* About Section */}
      <div className="sponsors-about-section">
        <h2 className="sponsors-about-heading">{currentContent.aboutHeading}</h2>
        <p className="sponsors-about-text">{currentContent.detailedDescription}</p>
        <p className="sponsors-about-text">{currentContent.organizationInfo}</p>
        <p className="sponsors-about-text">{currentContent.invitationText}</p>
        <div className="sponsors-contact-section">
          <p className="sponsors-contact-text">{currentContent.contactText}</p>
          <p className="sponsors-contact-name">Himakiran</p>
          <a
            href={`tel:${currentContent.contactNumber}`}
            className="sponsors-contact-number"
            style={{ textDecoration: 'none', color: 'inherit' }}
          >
            {currentContent.contactNumber}
          </a>
        </div>
      </div>
    </div>
  );
};

export default Sponsors;
