// Analytics utility functions for Sempozhil website
import { trackEvent, trackButtonClick, trackFormSubmission, trackExternalLink } from '../components/analytics/GoogleAnalytics';

// Track navigation events
export const trackNavigation = (pageName) => {
  trackEvent('navigation', 'page_visit', pageName);
};

// Track language changes
export const trackLanguageChange = (newLanguage, previousLanguage) => {
  trackEvent('language_change', 'user_preference', `${previousLanguage}_to_${newLanguage}`);
};

// Track audio player interactions
export const trackAudioPlayer = (action) => {
  trackEvent('audio_player', 'interaction', action);
};

// Track countdown timer interactions
export const trackCountdownTimer = (action) => {
  trackEvent('countdown_timer', 'interaction', action);
};

// Track school registration events
export const trackSchoolRegistration = (action, schoolType = null) => {
  const label = schoolType ? `${action}_${schoolType}` : action;
  trackEvent('school_registration', 'form_interaction', label);
};

// Track stall booking events
export const trackStallBooking = (action, stallType = null) => {
  const label = stallType ? `${action}_${stallType}` : action;
  trackEvent('stall_booking', 'form_interaction', label);
};

// Track sponsor/partner interactions
export const trackSponsorInteraction = (action, sponsorName = null) => {
  const label = sponsorName ? `${action}_${sponsorName}` : action;
  trackEvent('sponsor_interaction', 'engagement', label);
};

// Track team member interactions
export const trackTeamMemberInteraction = (action, memberName = null) => {
  const label = memberName ? `${action}_${memberName}` : action;
  trackEvent('team_interaction', 'engagement', label);
};

// Track social media clicks
export const trackSocialMediaClick = (platform, location) => {
  trackEvent('social_media', 'click', `${platform}_${location}`);
};

// Track phone number clicks
export const trackPhoneClick = (phoneNumber, location) => {
  trackEvent('contact', 'phone_click', `${phoneNumber}_${location}`);
};

// Track email clicks
export const trackEmailClick = (email, location) => {
  trackEvent('contact', 'email_click', `${email}_${location}`);
};

// Track form submissions with specific form types
export const trackFormSubmit = (formType, formData = {}) => {
  trackFormSubmission(formType);
  
  // Track additional form-specific data
  if (formType === 'school_registration' && formData.category) {
    trackEvent('form_submit', 'school_category', formData.category);
  }
  
  if (formType === 'stall_booking' && formData.stallType) {
    trackEvent('form_submit', 'stall_type', formData.stallType);
  }
};

// Track video interactions (for YouTube embeds)
export const trackVideoInteraction = (action, videoTitle = null) => {
  const label = videoTitle ? `${action}_${videoTitle}` : action;
  trackEvent('video', 'interaction', label);
};

// Track image gallery interactions
export const trackImageGallery = (action, imageName = null) => {
  const label = imageName ? `${action}_${imageName}` : action;
  trackEvent('image_gallery', 'interaction', label);
};

// Track download events (for brochures, forms, etc.)
export const trackDownloadEvent = (fileName, fileType, location) => {
  trackEvent('download', fileType, `${fileName}_${location}`);
};

// Track external website visits
export const trackExternalWebsite = (websiteName, url, location) => {
  trackExternalLink(url, `${websiteName}_${location}`);
};

// Track error events
export const trackError = (errorType, errorMessage, location) => {
  trackEvent('error', errorType, `${errorMessage}_${location}`);
};

// Track performance events
export const trackPerformance = (metricName, value, location) => {
  trackEvent('performance', metricName, location, value);
};

// Track user engagement events
export const trackEngagement = (action, element, timeSpent = null) => {
  const value = timeSpent ? Math.round(timeSpent) : undefined;
  trackEvent('engagement', action, element, value);
};

// Track scroll depth
export const trackScrollDepth = (percentage, pageName) => {
  trackEvent('scroll_depth', 'page_scroll', `${pageName}_${percentage}%`);
};

// Track time spent on page
export const trackTimeOnPage = (pageName, timeInSeconds) => {
  trackEvent('time_on_page', 'page_engagement', pageName, Math.round(timeInSeconds));
};

// Track search events (if you add search functionality)
export const trackSearch = (searchTerm, resultsCount = null) => {
  const value = resultsCount !== null ? resultsCount : undefined;
  trackEvent('search', 'site_search', searchTerm, value);
};

// Track filter usage (for schools, stalls, etc.)
export const trackFilter = (filterType, filterValue, location) => {
  trackEvent('filter', filterType, `${filterValue}_${location}`);
};

// Track modal/popup interactions
export const trackModal = (action, modalName) => {
  trackEvent('modal', action, modalName);
};

// Track button clicks with specific context
export const trackContextualButtonClick = (buttonText, context, location) => {
  trackButtonClick(`${buttonText}_${context}`, location);
};
