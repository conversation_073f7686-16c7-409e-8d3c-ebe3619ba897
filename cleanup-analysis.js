const fs = require('fs');
const path = require('path');

// List of original images that have been converted to responsive versions
const convertedImages = [
  // Gallery images
  'src/assets/homepage/cycling.jpg',
  'src/assets/homepage/jumping.jpg',
  'src/assets/homepage/karthick.jpg',
  'src/assets/homepage/karupannan.jpg',
  'src/assets/homepage/kuththattam.jpg',
  'src/assets/homepage/melam.jpg',
  'src/assets/homepage/poikaalkuthirai.jpg',
  'src/assets/homepage/sakthi.jpg',
  'src/assets/homepage/ural.jpg',
  'src/assets/homepage/uri.jpg',
  'src/assets/homepage/uriyadi.jpg',

  // Team images
  'src/assets/team/1.png',
  'src/assets/team/2.png',
  'src/assets/team/4.png',
  'src/assets/team/5.png',
  'src/assets/team/6.png',
  'src/assets/team/7.png',
  'src/assets/team/81.png',
  'src/assets/team/Suresh-Chandra.jpg',
  'src/assets/team/Sudha.jpg',

  // Icons
  'src/assets/close.png',
  'src/assets/instrument.png',
  'src/assets/locationicon.png',

  // Social media icons
  'src/assets/social-media/facebook.png',
  'src/assets/social-media/instagram.png',
  'src/assets/social-media/linkedin.png',
  'src/assets/social-media/twitter-alt-circle.png',
  'src/assets/social-media/youtube.png',
  'src/assets/social-media/black_facebook.png',
  'src/assets/social-media/black-instagram.png',
  'src/assets/social-media/black-linkedIn.png',
  'src/assets/social-media/balck-X.png',

  // Logos
  'src/assets/logo.png',
  'src/assets/image-layouts/maintitlem.png',
  'src/assets/image-layouts/maintitlel.png',

  // Sponsors
  'src/assets/sponsors/footeLogo_1.png',
  'src/assets/sponsors/footeLogo_2.png',
  'src/assets/sponsors/fm_patner.png',
  'src/assets/sponsors/Ability_patner.png',
  'src/assets/sponsors/zero_food_waste patner.png',
  'src/assets/sponsors/media_patner.png',
  'src/assets/sponsors/dcc.png',
  'src/assets/sponsors/beauty_partner.png',
  'src/assets/sponsors/good_food_patner.png',
  'src/assets/sponsors/Knowledge Partner.png',

  // Schools
  'src/assets/schools/reconnect.jpg',
  'src/assets/schools/Interact with Animals.jpg',
  'src/assets/schools/Traditional Handicraft.jpg',
  'src/assets/schools/Tradtional Games.jpg',
  'src/assets/schools/Native Food.jpg',
  'src/assets/schools/Digital Detox.jpg',
  'src/assets/schools/school1.jpeg',
  'src/assets/schools/school2.jpeg',

  // Snapshots
  'src/assets/homepage/snapshot/30K foot fall 5K children.jpg',
  'src/assets/homepage/snapshot/200+ Animals 40+ Breeds.jpg',
  'src/assets/homepage/snapshot/100+ Performance Artists.jpg',
  'src/assets/homepage/snapshot/20+ exhibits.jpg',
  'src/assets/homepage/snapshot/150+ stalls.jpg',
  'src/assets/homepage/snapshot/50k footfall 15k children.jpg',
  'src/assets/homepage/snapshot/250+ animals 40+ breads.jpg',
  'src/assets/homepage/snapshot/150+ performance artists.jpg',
  'src/assets/homepage/snapshot/25+ exhibits the land, it\'s history, the people.jpg',
  'src/assets/homepage/snapshot/250+ stalls.jpg',
  'src/assets/homepage/snapshot/20+ Traditional Skills.jpg',

  // Spotlights
  'src/assets/homepage/spotlights/Picture1.png',
  'src/assets/homepage/spotlights/Picture2.png',
  'src/assets/homepage/spotlights/govt1.png',
  'src/assets/homepage/spotlights/govt2.png',
  'src/assets/homepage/spotlights/tv1.png',
  'src/assets/homepage/spotlights/tv2.png',
  'src/assets/homepage/spotlights/tv3.png',
  'src/assets/homepage/spotlights/tv4.png',
  'src/assets/homepage/spotlights/tv5.png',
  'src/assets/homepage/spotlights/tv6.png',

  // Website logos
  'src/assets/website-logo/nammaform.jpg',
  'src/assets/website-logo/thean.jpg',
  'src/assets/website-logo/millet.jpg',
  'src/assets/website-logo/Palmy.png',
  'src/assets/website-logo/thonadai.jpg',
  'src/assets/website-logo/nn.jpg',
  'src/assets/website-logo/amazon.jpg',
  'src/assets/website-logo/7.jpg',
  'src/assets/website-logo/<EMAIL>',
  'src/assets/website-logo/it-to-agri.jpg'
];

// Images that should NOT be removed (still needed)
const keepImages = [
  // Background images - these are the NEW optimized ones we copied
  'src/assets/mobile/backgrounds/landing-mobile.jpg',
  'src/assets/laptop/backgrounds/landing-laptop.jpg',
  'src/assets/monitor/backgrounds/landing-monitor.jpg',

  // Old background images that might still be referenced
  'src/assets/homepage/landingl.jpg',
  'src/assets/homepage/landingm.jpg',
  'src/assets/landing pageMobile.jpg',

  // Festival gallery images (not yet converted)
  'src/assets/image-layouts/festival-gallery-1.jpg',
  'src/assets/image-layouts/festival-gallery-2.jpg',
  'src/assets/image-layouts/festival-gallery-3.jpg',
  'src/assets/image-layouts/festival-gallery-4.jpg',
  'src/assets/image-layouts/festival-gallery-5.jpg',
  'src/assets/image-layouts/festival-gallery-6.jpg',
  'src/assets/image-layouts/festival-gallery-7.jpg',
  'src/assets/image-layouts/festival-gallery-8.jpg',
  'src/assets/image-layouts/festival-main-1.jpg',
  'src/assets/image-layouts/festival-main-2.jpg'
];

function analyzeCleanup() {
  console.log('🔍 Analyzing image cleanup opportunities...\n');

  let totalSize = 0;
  let canRemoveSize = 0;
  let canRemoveCount = 0;
  let keepCount = 0;

  console.log('📊 CLEANUP ANALYSIS REPORT\n');
  console.log('=' .repeat(60));

  console.log('\n✅ IMAGES THAT CAN BE SAFELY REMOVED:');
  console.log('(These have been converted to responsive versions)\n');

  convertedImages.forEach(imagePath => {
    if (fs.existsSync(imagePath)) {
      const stats = fs.statSync(imagePath);
      const sizeKB = Math.round(stats.size / 1024);
      totalSize += stats.size;
      canRemoveSize += stats.size;
      canRemoveCount++;
      console.log(`  ✓ ${imagePath} (${sizeKB} KB)`);
    } else {
      console.log(`  ⚠️  ${imagePath} (not found)`);
    }
  });

  console.log('\n🔒 IMAGES TO KEEP:');
  console.log('(These are still needed or not yet converted)\n');

  keepImages.forEach(imagePath => {
    if (fs.existsSync(imagePath)) {
      const stats = fs.statSync(imagePath);
      const sizeKB = Math.round(stats.size / 1024);
      totalSize += stats.size;
      keepCount++;
      console.log(`  🔒 ${imagePath} (${sizeKB} KB)`);
    } else {
      console.log(`  ⚠️  ${imagePath} (not found)`);
    }
  });

  console.log('\n' + '=' .repeat(60));
  console.log('📈 SUMMARY:');
  console.log(`  • Images that can be removed: ${canRemoveCount}`);
  console.log(`  • Images to keep: ${keepCount}`);
  console.log(`  • Space that can be freed: ${Math.round(canRemoveSize / 1024)} KB`);
  console.log(`  • Total original images size: ${Math.round(totalSize / 1024)} KB`);
  console.log(`  • Space savings: ${Math.round((canRemoveSize / totalSize) * 100)}%`);

  console.log('\n💡 RECOMMENDATION:');
  console.log('  The original images listed above can be safely removed since they');
  console.log('  have been converted to device-specific responsive versions.');
  console.log('  This will free up significant space while maintaining all functionality.');

  console.log('\n⚠️  IMPORTANT:');
  console.log('  Before removing, ensure all components are using the responsive');
  console.log('  image hook and test the website thoroughly on all device types.');
}

// Run the analysis
analyzeCleanup();
