version: 1
# Frontend-only React app configuration
# No backend resources needed
frontend:
  # Environment variables for frontend build
  environmentVariables:
    NODE_ENV: production
    GENERATE_SOURCEMAP: false
    DISABLE_ESLINT_PLUGIN: false
  phases:
    preBuild:
      commands:
        - npm ci --cache .npm --prefer-offline
        - echo "Environment check - NODE_ENV=$NODE_ENV"
        - echo "Environment check - PUBLIC_URL=$PUBLIC_URL"
        - echo "Build environment - hostname=$(hostname)"
        - echo "Build environment - pwd=$(pwd)"
        - echo "Build environment - ls public/assets/audio/"
        - ls public/assets/audio/ || echo "Audio directory not found"
    build:
      commands:
        - npm run build
        - echo "Build completed - checking static assets"
        - ls build/static/media/ | grep -E "\.(mp3|jpeg|jpg|png)$" || echo "No media assets found"
  artifacts:
    baseDirectory: build
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
      - .npm/**/*
  customHeaders:
    - pattern: '/assets/**'
      headers:
        - key: 'Cache-Control'
          value: 'public, max-age=31536000, immutable'
        - key: 'Access-Control-Allow-Origin'
          value: '*'
    - pattern: '/static/**'
      headers:
        - key: 'Cache-Control'
          value: 'public, max-age=31536000, immutable'
        - key: 'Access-Control-Allow-Origin'
          value: '*'
    - pattern: '**/*.mp3'
      headers:
        - key: 'Content-Type'
          value: 'audio/mpeg'
        - key: 'Access-Control-Allow-Origin'
          value: '*'
    - pattern: '**/*.jpeg'
      headers:
        - key: 'Content-Type'
          value: 'image/jpeg'
        - key: 'Access-Control-Allow-Origin'
          value: '*'
    - pattern: '**/*.jpg'
      headers:
        - key: 'Content-Type'
          value: 'image/jpeg'
        - key: 'Access-Control-Allow-Origin'
          value: '*'
    - pattern: '**/*.png'
      headers:
        - key: 'Content-Type'
          value: 'image/png'
        - key: 'Access-Control-Allow-Origin'
          value: '*'
