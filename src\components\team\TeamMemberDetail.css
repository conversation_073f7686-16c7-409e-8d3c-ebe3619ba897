/* Team Member Detail View Styles */
.team-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999; /* Higher than navbar (1000) and countdown (1000) */
  padding: 1rem;
  overflow-y: auto;
}

.team-detail-container {
  position: relative;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  background: #ffffff;
  border-radius: 10px;
  overflow-y: auto;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.close-button {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 10000; /* Ensure close button is above everything */
}

.close-button button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.close-button button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.close-button img {
  width: 30px;
  height: 30px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 3rem 2rem 2rem;
  text-align: center;
}

.detail-image {
  margin-bottom: 2rem;
}

.detail-image img {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  object-fit: cover;
  border: 5px solid #f0f0f0;
}

.detail-name {
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  font-size: 2.5rem;
  font-weight: 700;
  color: #4B0000; /* Deep Maroon */
  margin: 0 0 1rem 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  line-height: 1.2;
}

.detail-role {
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  font-size: 1.3rem;
  font-weight: 600;
  color: #1E1E1E; /* Dark Charcoal */
  margin: 0 0 2rem 0;
  text-transform: capitalize;
  line-height: 1.3;
  letter-spacing: 0.3px;
}

.website-links {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  justify-content: center;
}

.website-link {
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #DAA520, #B8860B); /* Traditional Gold gradient */
  color: #ffffff;
  text-decoration: none;
  border-radius: 5px;
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 600;
  transition: all 0.3s ease;
}

.website-link:hover {
  background: linear-gradient(135deg, #B8860B, #9A7209);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(218, 165, 32, 0.4);
}

/* Website Logo Links */
.website-logo-link {
  display: inline-block;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0 0.5rem;
}

.website-logo-link:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

.website-logo-link img {
  width: 80px;
  height: 80px;
  object-fit: contain;
  background: white;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.website-logo-link:hover img {
  transform: scale(1.05);
}

.bio-content {
  max-width: 700px;
  text-align: justify;
  line-height: 1.8;
}

.bio-content p {
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 1.1rem;
  color: #333333; /* Dark Gray */
  margin-bottom: 1.5rem;
  font-weight: 400;
  line-height: 1.7;
}

/* Mobile Responsive */
@media screen and (max-width: 768px) {
  .team-detail-overlay {
    padding: 0.5rem;
  }
  
  .detail-content {
    padding: 2rem 1rem 1rem;
  }
  
  .detail-image img {
    width: 150px;
    height: 150px;
  }
  
  .detail-name {
    font-size: 2rem;
    text-shadow:
      -1px -1px 0 #ffffff,
      1px -1px 0 #ffffff,
      -1px 1px 0 #ffffff,
      1px 1px 0 #fffcfc,
      -2px 0 0 #ffffff,
      2px 0 0 #ffffff,
      0 -2px 0 #e6e1e1,
      0 2px 0 #e4d6d6;
  }

  .detail-role {
    font-size: 1.1rem;
  }

  .bio-content p {
    font-size: 1rem;
  }
  
  .close-button {
    top: 10px;
    right: 10px;
  }
  
  .close-button img {
    width: 25px;
    height: 25px;
  }

  .website-logo-link img {
    width: 60px;
    height: 60px;
    padding: 6px;
  }
}

/* Large Desktop */
@media screen and (min-width: 1200px) {
  .team-detail-container {
    max-width: 900px;
  }
  
  .detail-content {
    padding: 4rem 3rem 3rem;
  }
  
  .detail-image img {
    width: 250px;
    height: 250px;
  }
  
  .detail-name {
    font-size: 3rem;
  }
  
  .bio-content p {
    font-size: 1.2rem;
    line-height: 2;
  }
}
