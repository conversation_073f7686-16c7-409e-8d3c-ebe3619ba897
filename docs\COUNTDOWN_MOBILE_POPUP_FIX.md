# Countdown Timer Mobile Popup Fix

## Problem Description
The countdown wrapper at the bottom of the screen was overlapping and hiding team member "Know More" popup content on mobile devices, making the popups unreadable and unusable. This issue only affected mobile screens where the fixed countdown wrapper would cover the popup content.

## Solution Implemented

### 1. State Management
- Added `isTeamPopupOpen` state to the main App component
- Created a callback function `handleTeamPopupToggle` to manage popup state
- Passed the popup state down to the CountdownTimer component

### 2. Team Component Updates
**File: `src/components/team/Team.js`**
- Added `onPopupToggle` prop to receive callback from parent
- Modified `handleKnowMore` function to notify parent when popup opens
- Modified `handleCloseDetail` function to notify parent when popup closes

```javascript
const handleKnowMore = (member) => {
  setSelectedMember(member);
  setShowDetail(true);
  // Notify parent component that popup is open
  if (onPopupToggle) {
    onPopupToggle(true);
  }
};

const handleCloseDetail = () => {
  setShowDetail(false);
  setSelectedMember(null);
  // Notify parent component that popup is closed
  if (onPopupToggle) {
    onPopupToggle(false);
  }
};
```

### 3. CountdownTimer Component Updates
**File: `src/components/common/CountdownTimer.js`**
- Added `isTeamPopupOpen` prop to receive popup state
- Implemented conditional CSS class application
- Added dynamic class name generation based on popup state

```javascript
const CountdownTimer = ({ language = 'english', isTeamPopupOpen = false }) => {
  // Generate dynamic class name based on popup state
  // When team popup is open, hide countdown wrapper on mobile screens only
  // Desktop/tablet screens will continue to show the countdown wrapper
  const countdownWrapperClass = `countdown-wrapper ${isTeamPopupOpen ? 'hidden-on-mobile' : ''}`;

  return (
    <div className={countdownWrapperClass}>
      {/* ... rest of component */}
    </div>
  );
};
```

### 4. CSS Implementation
**File: `src/components/common/CountdownTimer.css`**
- Added responsive CSS rules to hide countdown wrapper on mobile when popup is open
- Ensured countdown wrapper remains visible on desktop/tablet screens

```css
/* Hide countdown wrapper on mobile when team popup is open */
/* Only hide on mobile screens (768px and below) */
@media (max-width: 768px) {
  .countdown-wrapper.hidden-on-mobile {
    display: none !important;
  }
}

/* Remain visible on tablet and desktop screens */
@media (min-width: 769px) {
  .countdown-wrapper.hidden-on-mobile {
    display: block !important;
  }
}
```

### 5. App Component Integration
**File: `src/App.js`**
- Added state management for popup visibility
- Passed popup state to CountdownTimer component
- Connected Team component with popup toggle callback

## Key Features

### ✅ Mobile-Specific Hiding
- Countdown wrapper is hidden **only on mobile screens** (≤768px) when popup is open
- Desktop and tablet users continue to see the countdown wrapper even with popups open

### ✅ Automatic State Management
- Popup state is automatically tracked when users open/close team member details
- No manual intervention required - works seamlessly with existing popup functionality

### ✅ Responsive Design
- Solution respects existing responsive breakpoints
- Maintains countdown functionality on larger screens where overlap isn't an issue

### ✅ Performance Optimized
- Uses CSS classes for hiding/showing instead of JavaScript DOM manipulation
- Minimal performance impact with conditional class application

## Testing Scenarios

### Mobile Devices (≤768px)
1. **Popup Closed**: Countdown wrapper visible at bottom
2. **Popup Open**: Countdown wrapper hidden, popup fully readable
3. **Popup Closed Again**: Countdown wrapper reappears

### Desktop/Tablet (>768px)
1. **Popup Closed**: Countdown wrapper visible at bottom
2. **Popup Open**: Countdown wrapper remains visible, popup displays above it
3. **Both Elements Visible**: No overlap issues on larger screens

## Browser Compatibility
- Works with all modern browsers
- CSS media queries provide reliable responsive behavior
- Fallback behavior: countdown wrapper remains visible if JavaScript fails

## Future Enhancements
- Could be extended to hide countdown wrapper for other modal/popup components
- State management could be moved to a context provider for more complex applications
- Additional breakpoints could be added for different device sizes

## Files Modified
1. `src/App.js` - State management and prop passing
2. `src/components/team/Team.js` - Popup state notifications
3. `src/components/common/CountdownTimer.js` - Conditional class application
4. `src/components/common/CountdownTimer.css` - Responsive hiding styles
