const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// Device-specific configurations
const deviceConfigs = {
  mobile: {
    gallery: { width: 400, height: 300, quality: 75 },
    team: { width: 150, height: 150, quality: 85 },
    icons: { width: 24, height: 24, quality: 90 },
    socialIcons: { width: 24, height: 24, quality: 90 },
    'social-media': { width: 24, height: 24, quality: 90 },
    logos: { width: 200, height: 80, quality: 90 },
    sponsors: { width: 80, height: 40, quality: 85 },
    schools: { width: 300, height: 200, quality: 80 },
    posters: { width: 400, height: 600, quality: 90 },
    snapshots: { width: 300, height: 200, quality: 90 },
    spotlights: { width: 350, height: 250, quality: 90 },
    'website-logos': { width: 60, height: 30, quality: 85 }
  },
  laptop: {
    gallery: { width: 600, height: 450, quality: 85 },
    team: { width: 200, height: 200, quality: 85 },
    icons: { width: 32, height: 32, quality: 90 },
    socialIcons: { width: 32, height: 32, quality: 90 },
    'social-media': { width: 32, height: 32, quality: 90 },
    logos: { width: 300, height: 120, quality: 90 },
    sponsors: { width: 120, height: 60, quality: 85 },
    schools: { width: 400, height: 300, quality: 85 },
    posters: { width: 600, height: 900, quality: 90 },
    snapshots: { width: 450, height: 300, quality: 90 },
    spotlights: { width: 500, height: 350, quality: 90 },
    'website-logos': { width: 80, height: 40, quality: 85 }
  },
  monitor: {
    gallery: { width: 800, height: 600, quality: 90 },
    team: { width: 250, height: 250, quality: 85 },
    icons: { width: 48, height: 48, quality: 90 },
    socialIcons: { width: 40, height: 40, quality: 90 },
    'social-media': { width: 40, height: 40, quality: 90 },
    logos: { width: 400, height: 160, quality: 90 },
    sponsors: { width: 160, height: 80, quality: 85 },
    schools: { width: 500, height: 375, quality: 90 },
    posters: { width: 800, height: 1200, quality: 90 },
    snapshots: { width: 600, height: 400, quality: 90 },
    spotlights: { width: 700, height: 500, quality: 90 },
    'website-logos': { width: 100, height: 50, quality: 85 }
  }
};

// Image mappings
const imageMappings = {
  gallery: [
    'src/assets/homepage/cycling.jpg',
    'src/assets/homepage/jumping.jpg',
    'src/assets/homepage/karthick.jpg',
    'src/assets/homepage/karupannan.jpg',
    'src/assets/homepage/kuththattam.jpg',
    'src/assets/homepage/melam.jpg',
    'src/assets/homepage/poikaalkuthirai.jpg',
    'src/assets/homepage/sakthi.jpg',
    'src/assets/homepage/ural.jpg',
    'src/assets/homepage/uri.jpg',
    'src/assets/homepage/uriyadi.jpg'
  ],
  team: [
    'src/assets/team/1.png',
    'src/assets/team/2.png',
    'src/assets/team/4.png',
    'src/assets/team/5.png',
    'src/assets/team/6.png',
    'src/assets/team/7.png',
    'src/assets/team/81.png',
    'src/assets/team/Suresh-Chandra.jpg',
    'src/assets/team/Sudha.jpg'
  ],
  icons: [
    'src/assets/close.png',
    'src/assets/instrument.png',
    'src/assets/locationicon.png'
  ],
  socialIcons: [
    'src/assets/social-media/facebook.png',
    'src/assets/social-media/instagram.png',
    'src/assets/social-media/linkedin.png',
    'src/assets/social-media/twitter-alt-circle.png',
    'src/assets/social-media/youtube.png'
  ],
  'social-media': [
    'src/assets/social-media/black_facebook.png',
    'src/assets/social-media/black-instagram.png',
    'src/assets/social-media/black-linkedIn.png',
    'src/assets/social-media/balck-X.png'
  ],
  logos: [
    'src/assets/logo.png',
    'src/assets/image-layouts/maintitlem.png',
    'src/assets/image-layouts/maintitlel.png'
  ],
  sponsors: [
    'src/assets/sponsors/footeLogo_1.png',
    'src/assets/sponsors/footeLogo_2.png',
    'src/assets/sponsors/fm_patner.png',
    'src/assets/sponsors/Ability_patner.png',
    'src/assets/sponsors/zero_food_waste patner.png',
    'src/assets/sponsors/media_patner.png',
    'src/assets/sponsors/dcc.png',
    'src/assets/sponsors/beauty_partner.png',
    'src/assets/sponsors/good_food_patner.png',
    'src/assets/sponsors/Knowledge Partner.png'
  ],
  schools: [
    'src/assets/schools/reconnect.jpg',
    'src/assets/schools/Interact with Animals.jpg',
    'src/assets/schools/Traditional Handicraft.jpg',
    'src/assets/schools/Tradtional Games.jpg',
    'src/assets/schools/Native Food.jpg',
    'src/assets/schools/Digital Detox.jpg'
  ],
  posters: [
    'src/assets/schools/school1.jpeg',
    'src/assets/schools/school2.jpeg'
  ],
  snapshots: [
    'src/assets/homepage/snapshot/30K foot fall 5K children.jpg',
    'src/assets/homepage/snapshot/200+ Animals 40+ Breeds.jpg',
    'src/assets/homepage/snapshot/100+ Performance Artists.jpg',
    'src/assets/homepage/snapshot/20+ exhibits.jpg',
    'src/assets/homepage/snapshot/150+ stalls.jpg',
    'src/assets/homepage/snapshot/50k footfall 15k children.jpg',
    'src/assets/homepage/snapshot/250+ animals 40+ breads.jpg',
    'src/assets/homepage/snapshot/150+ performance artists.jpg',
    'src/assets/homepage/snapshot/25+ exhibits the land, it\'s history, the people.jpg',
    'src/assets/homepage/snapshot/250+ stalls.jpg',
    'src/assets/homepage/snapshot/20+ Traditional Skills.jpg'
  ],
  spotlights: [
    'src/assets/homepage/spotlights/Picture1.png',
    'src/assets/homepage/spotlights/Picture2.png',
    'src/assets/homepage/spotlights/govt1.png',
    'src/assets/homepage/spotlights/govt2.png',
    'src/assets/homepage/spotlights/tv1.png',
    'src/assets/homepage/spotlights/tv2.png',
    'src/assets/homepage/spotlights/tv3.png',
    'src/assets/homepage/spotlights/tv4.png',
    'src/assets/homepage/spotlights/tv5.png',
    'src/assets/homepage/spotlights/tv6.png'
  ],
  'website-logos': [
    'src/assets/website-logo/nammaform.jpg',
    'src/assets/website-logo/thean.jpg',
    'src/assets/website-logo/millet.jpg',
    'src/assets/website-logo/Palmy.png',
    'src/assets/website-logo/thonadai.jpg',
    'src/assets/website-logo/nn.jpg',
    'src/assets/website-logo/amazon.jpg',
    'src/assets/website-logo/7.jpg',
    'src/assets/website-logo/<EMAIL>',
    'src/assets/website-logo/it-to-agri.jpg'
  ]
};

// Function to ensure directory exists
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// Function to get output filename
function getOutputFilename(inputPath, device, category) {
  const basename = path.basename(inputPath, path.extname(inputPath));
  const ext = path.extname(inputPath);
  return `${basename}${ext}`;
}

// Function to resize image
async function resizeImage(inputPath, outputPath, config) {
  try {
    console.log(`Resizing: ${inputPath} -> ${outputPath}`);
    
    if (!fs.existsSync(inputPath)) {
      console.log(`Warning: Source file not found: ${inputPath}`);
      return false;
    }

    await sharp(inputPath)
      .resize(config.width, config.height, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({ quality: config.quality })
      .toFile(outputPath);
    
    console.log(`✅ Successfully resized: ${outputPath}`);
    return true;
  } catch (error) {
    console.error(`❌ Error resizing ${inputPath}:`, error.message);
    return false;
  }
}

// Main function to process all images
async function processAllImages() {
  console.log('🚀 Starting image optimization process...\n');
  
  let totalProcessed = 0;
  let totalSuccess = 0;

  for (const [category, imagePaths] of Object.entries(imageMappings)) {
    console.log(`\n📁 Processing ${category} images...`);
    
    for (const imagePath of imagePaths) {
      for (const [device, configs] of Object.entries(deviceConfigs)) {
        if (configs[category]) {
          const outputDir = `src/assets/${device}/${category}`;
          ensureDirectoryExists(outputDir);
          
          const outputFilename = getOutputFilename(imagePath, device, category);
          const outputPath = path.join(outputDir, outputFilename);
          
          totalProcessed++;
          const success = await resizeImage(imagePath, outputPath, configs[category]);
          if (success) totalSuccess++;
        }
      }
    }
  }

  console.log(`\n🎉 Image optimization complete!`);
  console.log(`📊 Results: ${totalSuccess}/${totalProcessed} images processed successfully`);
  
  if (totalSuccess < totalProcessed) {
    console.log(`⚠️  ${totalProcessed - totalSuccess} images failed to process`);
  }
}

// Run the script
processAllImages().catch(console.error);
