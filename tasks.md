task1 :
actully in the codebase UI is many mistakes in here that wanna make perfect the mistake is many section it's all in mylaptop size only for eg: Sempozhil 2025 - A Sneak Peek this section have 6 cards and this heading "Sempozhil 2025 - A Sneak Peek" if i wanna see this section this entire section should be show and fir in the screen if i go to see the heading "Sempozhil 2025 - A Sneak Peek" top of the screen the second row cards hiding 50% in bottom screen if i wanna see that second row images cards the heading is hided top of the screen so i wanna fit this you can reduce the size of the cards i need like this for every pages every sections 
what are all the section needs like this fittable:
Home Page - Sempozhil 2025 - A Sneak Peek section
Sempozhil 2024 - A Snapshot section
Print Media section that two images 
TV Channels section that 4 images
What People Say section that 4 cards please ensure the overlaping over laying over flow shold avoid that 
shools page:
school posters image size 
sponsors page:
 About this Event section 

 other wise all are ok keep mind do not change any existing design pattern or functinality



 task2:
 make a proper theme for the website i mean texts one page have full black one page blue with white border i don't like this i need single pallete for this exept countdown wrapper, navbar heading and navbar other wise all are wnna change the styles like fonts, text colors, backgrounds like that transparent background some places are good but some places are not transparent i think and make it perfect also the buttons need a proper colors


 task3:
