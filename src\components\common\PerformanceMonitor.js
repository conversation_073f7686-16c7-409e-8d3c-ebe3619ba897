import { useEffect } from 'react';
import { logWarning } from '../../utils/errorSuppression';

const PerformanceMonitor = () => {
  useEffect(() => {
    // Only run in production
    // Don't rely on process.env as it may not be available in AWS Amplify
    const isProduction = (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'production') || window.location.hostname !== 'localhost';

    if (!isProduction) return;

    // Monitor and suppress YouTube embed violations
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    
    EventTarget.prototype.addEventListener = function(type, listener, options) {
      // Check if this is a YouTube embed iframe
      const isYouTubeEmbed = this.src && this.src.includes('youtube.com/embed');
      const isScrollBlocking = ['touchstart', 'touchmove', 'touchend', 'wheel'].includes(type);
      
      if (isYouTubeEmbed && isScrollBlocking) {
        // Force passive option for YouTube embeds
        const passiveOptions = typeof options === 'object' 
          ? { ...options, passive: true }
          : { passive: true, capture: !!options };
          
        return originalAddEventListener.call(this, type, listener, passiveOptions);
      }
      
      return originalAddEventListener.call(this, type, listener, options);
    };

    // Monitor performance violations
    const performanceObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry) => {
        // Filter out YouTube-related performance issues
        if (entry.name && (
          entry.name.includes('youtube.com') ||
          entry.name.includes('www-embed-player') ||
          entry.name.includes('base.js')
        )) {
          // Suppress YouTube performance warnings
          return;
        }
        
        // Log other performance issues
        if (entry.duration > 100) {
          logWarning(`Performance issue: ${entry.name} took ${entry.duration}ms`, 'PerformanceMonitor');
        }
      });
    });

    // Observe long tasks
    try {
      performanceObserver.observe({ entryTypes: ['longtask'] });
    } catch (error) {
      // Long task observer not supported in all browsers
      logWarning('Long task observer not supported', 'PerformanceMonitor');
    }

    // Monitor memory usage (if available)
    const monitorMemory = () => {
      if (performance.memory) {
        const memoryInfo = performance.memory;
        const usedMB = Math.round(memoryInfo.usedJSHeapSize / 1048576);
        const limitMB = Math.round(memoryInfo.jsHeapSizeLimit / 1048576);
        
        // Warn if memory usage is high
        if (usedMB > limitMB * 0.8) {
          logWarning(`High memory usage: ${usedMB}MB / ${limitMB}MB`, 'PerformanceMonitor');
        }
      }
    };

    // Check memory every 30 seconds
    const memoryInterval = setInterval(monitorMemory, 30000);

    // Cleanup
    return () => {
      performanceObserver.disconnect();
      clearInterval(memoryInterval);
      
      // Restore original addEventListener
      EventTarget.prototype.addEventListener = originalAddEventListener;
    };
  }, []);

  // This component doesn't render anything
  return null;
};

export default PerformanceMonitor;
