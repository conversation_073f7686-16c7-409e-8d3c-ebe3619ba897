<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/assets/logo.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
    <meta name="theme-color" content="#000000" />

    <!-- iOS Safari specific meta tags -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Sempozhil 2025" />
    <meta name="format-detection" content="telephone=no" />
    <meta
      name="description"
      content="Sempozhil 2025 - Chennai's Grand Village Festival and Trade Expo. August 21-24 at YMCA Nandanam. Celebrating tradition, community, and sustainability."
    />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://sempozhil.com/" />
    <meta property="og:title" content="Sempozhil 2025 - Chennai's Grand Village Festival" />
    <meta property="og:description" content="Sempozhil 2025 - Chennai's Grand Village Festival and Trade Expo. August 21-24 at YMCA Nandanam. Celebrating tradition, community, and sustainability." />
    <meta property="og:image" content="%PUBLIC_URL%/assets/logo.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://sempozhil.com/" />
    <meta property="twitter:title" content="Sempozhil 2025 - Chennai's Grand Village Festival" />
    <meta property="twitter:description" content="Sempozhil 2025 - Chennai's Grand Village Festival and Trade Expo. August 21-24 at YMCA Nandanam. Celebrating tradition, community, and sustainability." />
    <meta property="twitter:image" content="%PUBLIC_URL%/assets/logo.png" />

    <link rel="apple-touch-icon" href="%PUBLIC_URL%/assets/logo.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-SHDFHYDL1Z"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-SHDFHYDL1Z');
    </script>

    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>Sempozhil 2025 - Chennai's Grand Village Festival</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
