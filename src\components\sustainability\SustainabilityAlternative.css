/* Alternative Design: Compact Card Layout */

/* Card-based Layout Styles - Alternative Design */
.card-table-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-top: 40px;
  align-items: start;
}

.card-scope-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  height: auto;
  max-height: 600px;
  display: flex;
  flex-direction: column;
}

.card-scope-section:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* Compact title design with Tamil Cultural Colors */
.card-scope-title {
  background: linear-gradient(135deg, #4B0000, #8B0000); /* Deep Maroon gradient */
  color: white;
  padding: 15px 20px;
  font-size: 1.1rem;
  font-weight: 700;
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  text-align: center;
  margin: 0;
  letter-spacing: 0.5px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.emissions-avoided-card .card-scope-title {
  background: linear-gradient(135deg, #00796B, #004D40); /* Peacock Teal gradient */
  position: relative;
}

.emissions-avoided-card .card-scope-title::after {
  content: "⭐";
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
}

/* Compact rows container */
.card-rows-container {
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  max-height: calc(600px - 60px); /* Subtract title height */
  overflow-y: auto;
}

/* Compact table rows */
.card-table-row {
  padding: 15px 20px;
  border-bottom: 1px solid #f8f9fa;
  transition: background-color 0.2s ease;
}

.card-table-row:hover {
  background-color: rgba(230, 126, 34, 0.03);
}

.card-table-row:last-child {
  border-bottom: none;
}

/* Compact row header with Tamil Cultural Styling */
.card-row-header {
  font-weight: 700;
  color: #1E1E1E; /* Dark Charcoal */
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  font-size: 1rem;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #4B0000; /* Deep Maroon */
  line-height: 1.3;
  letter-spacing: 0.3px;
}

.emissions-avoided-card .card-row-header {
  border-bottom-color: #00796B; /* Peacock Teal */
}

/* Compact field layout */
.card-field {
  margin-bottom: 10px;
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 8px;
  align-items: start;
}

.card-field:last-child {
  margin-bottom: 0;
}

.card-field-label {
  font-weight: 500;
  color: #7f8c8d;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  white-space: nowrap;
  min-width: 80px;
}

.card-field-value {
  color: #333333; /* Tamil cultural body text */
  font-family: 'Inter', 'Open Sans', 'Poppins', sans-serif;
  font-size: 0.9rem;
  line-height: 1.5;
  font-weight: 400;
  word-wrap: break-word;
}

.card-emissions-value {
  font-weight: 700;
  font-size: 1rem;
  color: #B22222; /* Tamil Red */
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  text-align: right;
  letter-spacing: 0.3px;
}

.card-emissions-avoided {
  color: #00796B !important; /* Peacock Teal */
  font-family: 'Noto Serif Tamil', 'Merriweather', serif;
  font-size: 1.1rem;
  font-weight: 700;
  letter-spacing: 0.3px;
}

/* Special styling for emissions avoided card with Tamil Cultural Colors */
.emissions-avoided-card {
  border: 2px solid #00796B; /* Peacock Teal */
  box-shadow: 0 6px 25px rgba(0, 121, 107, 0.15);
  position: relative;
}

.emissions-avoided-card::before {
  content: "IMPORTANT";
  position: absolute;
  top: -8px;
  right: 15px;
  background: #00796B; /* Peacock Teal */
  color: white;
  padding: 3px 10px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  letter-spacing: 0.5px;
  z-index: 10;
}

/* Responsive adjustments */
@media (min-width: 1200px) {
  .card-table-container {
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
    max-width: 1200px;
    margin: 50px auto 0;
  }
  
  .card-scope-section {
    max-height: 650px;
  }
}

@media (max-width: 1024px) {
  .card-table-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .emissions-avoided-card {
    grid-column: span 2;
    max-width: 70%;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .card-table-container {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .emissions-avoided-card {
    grid-column: span 1;
    max-width: 100%;
    order: -1; /* Move to top */
  }
  
  .card-scope-section {
    max-height: none;
  }
  
  .card-rows-container {
    max-height: none;
    overflow-y: visible;
  }
}
