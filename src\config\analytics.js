// Google Analytics Configuration for Sempozhil 2025
// Update this file with your actual Google Analytics settings

export const ANALYTICS_CONFIG = {
  // Original Google Analytics 4 Measurement ID from sempozhil html project
  GA_MEASUREMENT_ID: 'G-SHDFHYDL1Z',
  
  // Enable/disable analytics based on environment
  ENABLED_IN_DEVELOPMENT: false,
  ENABLED_IN_PRODUCTION: true,
  
  // Custom dimensions (if you set them up in GA4)
  CUSTOM_DIMENSIONS: {
    language: 'custom_dimension_1',
    user_type: 'custom_dimension_2',
    event_interest: 'custom_dimension_3'
  },
  
  // Event tracking settings
  TRACK_SCROLL_DEPTH: true,
  TRACK_TIME_ON_PAGE: true,
  TRACK_FORM_INTERACTIONS: true,
  TRACK_BUTTON_CLICKS: true,
  TRACK_EXTERNAL_LINKS: true,
  
  // Scroll depth tracking thresholds (percentages)
  SCROLL_DEPTH_THRESHOLDS: [25, 50, 75, 90, 100],
  
  // Time on page tracking interval (seconds)
  TIME_ON_PAGE_INTERVAL: 30,
  
  // Enhanced ecommerce settings (if you plan to sell tickets/products)
  ENHANCED_ECOMMERCE: {
    ENABLED: false,
    CURRENCY: 'INR'
  }
};

// Helper function to check if analytics should be enabled
export const isAnalyticsEnabled = () => {
  const isProduction = (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'production') || window.location.hostname !== 'localhost';
  const isDevelopment = !isProduction;
  
  if (isProduction && ANALYTICS_CONFIG.ENABLED_IN_PRODUCTION) {
    return true;
  }
  
  if (isDevelopment && ANALYTICS_CONFIG.ENABLED_IN_DEVELOPMENT) {
    return true;
  }
  
  return false;
};

// Helper function to get the measurement ID
export const getMeasurementId = () => {
  return ANALYTICS_CONFIG.GA_MEASUREMENT_ID;
};

// Event categories for consistent tracking
export const EVENT_CATEGORIES = {
  NAVIGATION: 'navigation',
  FORM: 'form',
  BUTTON: 'button',
  EXTERNAL_LINK: 'external_link',
  AUDIO: 'audio_player',
  VIDEO: 'video',
  SOCIAL_MEDIA: 'social_media',
  CONTACT: 'contact',
  DOWNLOAD: 'download',
  ERROR: 'error',
  PERFORMANCE: 'performance',
  ENGAGEMENT: 'engagement',
  SCROLL: 'scroll_depth',
  TIME: 'time_on_page',
  SEARCH: 'search',
  FILTER: 'filter',
  MODAL: 'modal'
};

// Event actions for consistent tracking
export const EVENT_ACTIONS = {
  CLICK: 'click',
  SUBMIT: 'submit',
  PLAY: 'play',
  PAUSE: 'pause',
  STOP: 'stop',
  OPEN: 'open',
  CLOSE: 'close',
  VIEW: 'view',
  DOWNLOAD: 'download',
  SHARE: 'share',
  SEARCH: 'search',
  FILTER: 'filter',
  SCROLL: 'scroll',
  TIME_SPENT: 'time_spent'
};

// Page names for consistent tracking
export const PAGE_NAMES = {
  HOME: 'homepage',
  SCHOOLS: 'schools',
  STALL_BOOKING: 'stall_booking',
  SUSTAINABILITY: 'sustainability',
  SPONSORS: 'sponsors',
  TEAM: 'team'
};

export default ANALYTICS_CONFIG;
